package com.shalan.analytics.plugin.instrumentation

import com.android.build.api.instrumentation.AsmClassVisitorFactory
import com.android.build.api.instrumentation.ClassContext
import com.android.build.api.instrumentation.ClassData
import com.android.build.api.instrumentation.InstrumentationParameters
import com.shalan.analytics.plugin.utils.ErrorReporter
import com.shalan.analytics.plugin.utils.PluginLogger
import com.shalan.analytics.plugin.utils.TrackingAnnotationInfo
import org.gradle.api.provider.ListProperty
import org.gradle.api.provider.Property
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Optional
import org.objectweb.asm.AnnotationVisitor
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes

abstract class AnalyticsClassVisitorFactory :
    AsmClassVisitorFactory<AnalyticsClassVisitorFactory.Parameters> {
    interface Parameters : InstrumentationParameters {
        @get:Input
        val enabled: Property<Boolean>

        @get:Input
        @get:Optional
        val debugMode: Property<Boolean>

        @get:Input
        @get:Optional
        val trackActivities: Property<Boolean>

        @get:Input
        @get:Optional
        val trackFragments: Property<Boolean>

        @get:Input
        @get:Optional
        val trackComposables: Property<Boolean>

        @get:Input
        @get:Optional
        val includePackages: ListProperty<String>

        @get:Input
        @get:Optional
        val excludePackages: ListProperty<String>

        // Method tracking configuration parameters
        @get:Input
        @get:Optional
        val methodTrackingEnabled: Property<Boolean>

        @get:Input
        @get:Optional
        val maxParametersPerMethod: Property<Int>

        @get:Input
        @get:Optional
        val validateAnnotations: Property<Boolean>

        @get:Input
        @get:Optional
        val excludeMethods: ListProperty<String>

        @get:Input
        @get:Optional
        val includeClassPatterns: ListProperty<String>

        @get:Input
        @get:Optional
        val excludeClassPatterns: ListProperty<String>
    }

    override fun createClassVisitor(
        classContext: ClassContext,
        nextClassVisitor: ClassVisitor,
    ): ClassVisitor {
        val params = parameters.get()

        // Initialize PluginLogger with debug mode setting
        PluginLogger.setDebugMode(params.debugMode.getOrElse(false))

        // If plugin is disabled, just pass through
        if (!params.enabled.getOrElse(true)) {
            return nextClassVisitor
        }

        return AnalyticsClassVisitor(
            api = instrumentationContext.apiVersion.get(),
            nextClassVisitor = nextClassVisitor,
            parameters = params,
            className = classContext.currentClassData.className,
        )
    }

    override fun isInstrumentable(classData: ClassData): Boolean {
        val params = parameters.get()

        // Initialize PluginLogger with debug mode setting
        PluginLogger.setDebugMode(params.debugMode.getOrElse(false))

        // If plugin is disabled, don't instrument anything
        if (!params.enabled.getOrElse(true)) {
            PluginLogger.debug("Analytics Annotation Plugin is disabled")
            return false
        }

        val className = classData.className.replace('/', '.')
        PluginLogger.debug("Checking if class $className is instrumentable")

        // Skip system classes
        if (className.startsWith("android.") ||
            className.startsWith("androidx.") ||
            className.startsWith("java.") ||
            className.startsWith("kotlin.")
        ) {
            PluginLogger.debug("Skipping system class $className")
            return false
        }

        // Check include packages
        val includePackages = params.includePackages.getOrElse(emptyList())
        if (includePackages.isNotEmpty()) {
            val isIncluded =
                includePackages.any { packageName ->
                    className.startsWith(packageName)
                }
            if (!isIncluded) return false
        }

        // Check exclude packages
        val excludePackages = params.excludePackages.getOrElse(emptyList())
        if (excludePackages.isNotEmpty()) {
            val isExcluded =
                excludePackages.any { packageName ->
                    className.startsWith(packageName)
                }
            if (isExcluded) return false
        }

        PluginLogger.debug("Class $className is instrumentable")
        return true
    }

    private inner class AnalyticsClassVisitor(
        api: Int,
        nextClassVisitor: ClassVisitor,
        private val parameters: Parameters,
        private val className: String,
    ) : ClassVisitor(api, nextClassVisitor) {
        private var annotationInfo: TrackingAnnotationInfo? = null
        private var currentAnnotationInfo: TrackingAnnotationInfo? = null
        private var hasTrackScreenAnnotation = false
        private var hasTrackScreenComposableAnnotation = false
        private val methodsWithTrackAnnotation = mutableMapOf<String, MethodTrackInfo>()
        private var screenName: String? = null
        private var screenClass: String? = null
        private val annotationParameters: MutableMap<String, Any> = mutableMapOf()
        private var internalClassName: String = ""
        private var superClassName: String? = null
        private var isActivity: Boolean = false
        private var isFragment: Boolean = false
        private val methodsToInstrument = mutableListOf<String>()
        private var hasOnCreateMethod = false
        private var onCreateMethodAccess = 0
        private var implementsTrackedScreenParamsProvider: Boolean = false

        override fun visit(
            version: Int,
            access: Int,
            name: String?,
            signature: String?,
            superName: String?,
            interfaces: Array<out String>?,
        ) {
            super.visit(version, access, name, signature, superName, interfaces)

            // Store class information for transformation decisions
            name?.let {
                internalClassName = it
                val dotClassName = it.replace('/', '.')
                logDebug("AnalyticsClassVisitor: Processing class $dotClassName")
            }
            superClassName = superName

            // Check if the class implements TrackedScreenParamsProvider
            implementsTrackedScreenParamsProvider = interfaces?.contains("com/shalan/analytics/core/TrackedScreenParamsProvider") == true

            // Determine class type for proper transformation
            isActivity = isActivityClass(superName)
            isFragment = isFragmentClass(superName)

            name?.let { internalName ->
                val dotClassName = internalName.replace('/', '.')
                logDebug(
                    "AnalyticsClassVisitor: Class $dotClassName - isActivity: $isActivity, isFragment: $isFragment, superName: $superName",
                )
                logDebug("AnalyticsClassVisitor: Raw superName: '$superName'")
                logDebug("AnalyticsClassVisitor: Implements TrackedScreenParamsProvider: $implementsTrackedScreenParamsProvider")
            }
        }

        override fun visitAnnotation(
            descriptor: String?,
            visible: Boolean,
        ): AnnotationVisitor? {
            when (descriptor) {
                "Lcom/shalan/analytics/annotation/TrackScreen;" -> {
                    hasTrackScreenAnnotation = true
                    logDebug("AnalyticsClassVisitor: Found @TrackScreen annotation on class $className")
                    return TrackScreenAnnotationVisitor(super.visitAnnotation(descriptor, visible))
                }

                "Lcom/shalan/analytics/compose/TrackScreenComposable;" -> {
                    hasTrackScreenComposableAnnotation = true
                    logDebug("AnalyticsClassVisitor: Found @TrackScreenComposable annotation on class $className")
                    return TrackScreenComposableAnnotationVisitor(
                        super.visitAnnotation(
                            descriptor,
                            visible,
                        ),
                    )
                }
            }
            return super.visitAnnotation(descriptor, visible)
        }

        override fun visitMethod(
            access: Int,
            name: String?,
            descriptor: String?,
            signature: String?,
            exceptions: Array<out String>?,
        ): MethodVisitor {
            try {
                if (descriptor?.contains("Landroidx/compose/runtime/Composer;") == true) {
                    logDebug("FORCE_DEBUG: Found Composer method $name with descriptor $descriptor")
                }
                logDebug("AnalyticsClassVisitor: [MODIFIED] Visiting method '$name' with descriptor '$descriptor' in class $className")
                // Check if this is a method we should instrument
                if (shouldCollectMethod(name, descriptor)) {
                    methodsToInstrument.add(name ?: "")
                    logDebug("AnalyticsClassVisitor: Collected method $name for potential instrumentation")

                    // Store onCreate method info for later modification
                    if (name == "onCreate" && descriptor == "(Landroid/os/Bundle;)V") {
                        hasOnCreateMethod = true
                        onCreateMethodAccess = access
                    }
                }

                val methodVisitor = super.visitMethod(access, name, descriptor, signature, exceptions)

                // If this is a lifecycle method we want to instrument, wrap with our instrumenting visitor
                val isActivityOnCreate =
                    name == "onCreate" && descriptor == "(Landroid/os/Bundle;)V" && isActivity
                val isFragmentOnViewCreated =
                    name == "onViewCreated" && descriptor == "(Landroid/view/View;Landroid/os/Bundle;)V" && isFragment
                val isComposableFunction = isComposableMethod(name, descriptor)

                logDebug("FORCE_DEBUG: isComposableFunction result for $name = $isComposableFunction")

                logDebug(
                    "AnalyticsClassVisitor: Method $name - isActivity: $isActivity, " +
                        "isFragment: $isFragment, isComposable: $isComposableFunction",
                )

                logDebug("FORCE_DEBUG: About to check method path for $name")
                PluginLogger.forceDebug(
                    "FORCE_DEBUG: Method $name paths - onCreate: $isActivityOnCreate, onViewCreated: $isFragmentOnViewCreated",
                )

                if (isActivityOnCreate || isFragmentOnViewCreated) {
                    PluginLogger.forceDebug("FORCE_DEBUG: Taking lifecycle path for method: $name")
                    logDebug("AnalyticsClassVisitor: Wrapping $name method for potential instrumentation")
                    return LifecycleInstrumentingMethodVisitor(api, methodVisitor, name ?: "unknown")
                } else if (isComposableFunction) {
                    PluginLogger.forceDebug("FORCE_DEBUG: Taking composable path for method: $name")
                    PluginLogger.forceDebug("Creating ComposableMethodVisitor for $name")
                    logDebug("AnalyticsClassVisitor: Wrapping Composable $name method for potential instrumentation")
                    return ComposableMethodVisitor(api, methodVisitor, name ?: "unknown")
                }

                PluginLogger.forceDebug("FORCE_DEBUG: Taking TrackMethodVisitor path for method: $name")
                PluginLogger.forceDebug("FORCE_DEBUG: About to create TrackMethodVisitor for method: $name")

                // Check if this method might have @Track annotation and wrap with TrackMethodVisitor
                val methodTrackingEnabled = parameters.methodTrackingEnabled.getOrElse(true)
                val maxParametersPerMethod = parameters.maxParametersPerMethod.getOrElse(10)
                val excludeMethods = parameters.excludeMethods.getOrElse(emptyList()).toSet()

                PluginLogger.forceDebug(
                    "Creating TrackMethodVisitor for method: $name, enabled: $methodTrackingEnabled",
                )

                return TrackMethodVisitor(
                    api,
                    methodVisitor,
                    name ?: "unknown",
                    descriptor ?: "",
                    className,
                    access,
                    methodTrackingEnabled = methodTrackingEnabled,
                    maxParametersPerMethod = maxParametersPerMethod,
                    excludeMethods = excludeMethods,
                )
            } catch (e: Exception) {
                PluginLogger.forceDebug("FORCE_DEBUG: Exception in visitMethod for $name: ${e.message}")
                e.printStackTrace()
                throw e
            }
        }

        override fun visitEnd() {
            try {
                // Check if we found any tracking annotations AND have methods to instrument
                if ((hasTrackScreenAnnotation || hasTrackScreenComposableAnnotation) && methodsToInstrument.isNotEmpty()) {
                    annotationInfo = createAnnotationInfo()

                    if (annotationInfo != null) {
                        logDebug("AnalyticsClassVisitor: Processing class with tracking annotations: $className")
                        logDebug("AnalyticsClassVisitor: Found ${methodsToInstrument.size} methods to instrument")

                        // Perform deferred transformation now that we have all annotation info
                        performDeferredTransformation(annotationInfo!!)
                    }
                } else {
                    logDebug("AnalyticsClassVisitor: No tracking annotations found in $className or no instrumentable methods")
                }
            } catch (e: Exception) {
                logError("AnalyticsClassVisitor: Error processing class $className", e)
                ErrorReporter.reportError(
                    className = className,
                    errorType = ErrorReporter.ErrorType.TRANSFORMATION_ERROR,
                    message = "Failed to process class with modern instrumentation",
                    throwable = e,
                )
            }

            super.visitEnd()
        }

        private fun createAnnotationInfo(): TrackingAnnotationInfo? {
            return when {
                hasTrackScreenAnnotation ->
                    TrackingAnnotationInfo(
                        screenName = screenName ?: extractScreenNameFromClassName(className),
                        screenClass = screenClass?.takeIf { it.isNotEmpty() } ?: extractSimpleClassName(className),
                        annotationType = TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN,
                        className = className,
                    )

                hasTrackScreenComposableAnnotation ->
                    TrackingAnnotationInfo(
                        screenName = screenName ?: extractScreenNameFromClassName(className),
                        screenClass = screenClass?.takeIf { it.isNotEmpty() } ?: extractSimpleClassName(className),
                        annotationType = TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN_COMPOSABLE,
                        className = className,
                    )

                else -> null
            }
        }

        private fun performDeferredTransformation(annotationInfo: TrackingAnnotationInfo) {
            logDebug("AnalyticsClassVisitor: Performing deferred transformation for ${annotationInfo.annotationType}")

            // Store the annotation info for use by lifecycle visitors
            currentAnnotationInfo = annotationInfo

            when (annotationInfo.annotationType) {
                TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN -> {
                    // For Activities and Fragments, inject into lifecycle methods
                    injectTrackingCallIntoMethod(annotationInfo)
                    logDebug("AnalyticsClassVisitor: Injected tracking call for screen: ${annotationInfo.screenName}")
                }

                TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN_COMPOSABLE -> {
                    // For Composables, inject the tracking method
                    injectTrackingMethod(annotationInfo)
                    logDebug("AnalyticsClassVisitor: Composable tracking injection completed for ${annotationInfo.screenName}")
                }
            }
        }

        private fun injectTrackingCallIntoMethod(annotationInfo: TrackingAnnotationInfo) {
            // The actual injection happens in OnCreateInstrumentingMethodVisitor
            // We just need to make sure our tracking method exists
            injectTrackingMethod(annotationInfo)
        }

        private inner class LifecycleInstrumentingMethodVisitor(
            api: Int,
            private val delegate: MethodVisitor,
            private val methodName: String,
        ) : MethodVisitor(api, delegate) {
            private var hasSuperCall = false

            override fun visitMethodInsn(
                opcode: Int,
                owner: String?,
                name: String?,
                descriptor: String?,
                isInterface: Boolean,
            ) {
                // First, call the original method instruction
                delegate.visitMethodInsn(opcode, owner, name, descriptor, isInterface)

                // Check for super calls that we want to inject tracking after
                val shouldInject =
                    when {
                        // Activity onCreate: inject after super.onCreate()
                        methodName == "onCreate" &&
                            opcode == Opcodes.INVOKESPECIAL &&
                            name == "onCreate" &&
                            descriptor == "(Landroid/os/Bundle;)V" &&
                            !hasSuperCall -> {
                            logDebug("AnalyticsClassVisitor: Detected Activity onCreate super call - shouldInject=true")
                            true
                        }

                        // Fragment onViewCreated: inject after super.onViewCreated()
                        methodName == "onViewCreated" &&
                            opcode == Opcodes.INVOKESPECIAL &&
                            name == "onViewCreated" &&
                            descriptor == "(Landroid/view/View;Landroid/os/Bundle;)V" &&
                            !hasSuperCall -> {
                            logDebug("AnalyticsClassVisitor: Detected Fragment onViewCreated super call - shouldInject=true")
                            true
                        }

                        else -> false
                    }

                if (shouldInject && hasTrackScreenAnnotation && currentAnnotationInfo != null) {
                    hasSuperCall = true
                    logDebug("AnalyticsClassVisitor: Injecting tracking call after super.$name in $className")

                    // Call the injected __injectAnalyticsTracking method
                    delegate.visitVarInsn(Opcodes.ALOAD, 0) // Load 'this'
                    delegate.visitMethodInsn(
                        Opcodes.INVOKESPECIAL,
                        internalClassName,
                        "__injectAnalyticsTracking",
                        "()V",
                        false,
                    )
                }
            }

            override fun visitInsn(opcode: Int) {
                // Call the original instruction
                delegate.visitInsn(opcode)
            }
        }

        private inner class ComposableMethodVisitor(
            api: Int,
            private val delegate: MethodVisitor,
            private val methodName: String,
        ) : MethodVisitor(api, delegate) {
            private var methodHasTrackScreenComposableAnnotation = false
            private var methodScreenName: String? = null
            private var hasInjectedTracking = false

            override fun visitAnnotation(
                descriptor: String?,
                visible: Boolean,
            ): AnnotationVisitor? {
                when (descriptor) {
                    "Lcom/shalan/analytics/compose/TrackScreenComposable;" -> {
                        methodHasTrackScreenComposableAnnotation = true
                        hasTrackScreenComposableAnnotation = true
                        PluginLogger.forceDebug("Found @TrackScreenComposable annotation on method $methodName")
                        logDebug("AnalyticsClassVisitor: Found @TrackScreenComposable annotation on Composable method $methodName")
                        return ComposableAnnotationVisitor(
                            delegate.visitAnnotation(
                                descriptor,
                                visible,
                            ),
                        )
                    }
                }
                return delegate.visitAnnotation(descriptor, visible)
            }

            override fun visitCode() {
                // Call the original visitCode
                delegate.visitCode()

                // Inject tracking call at the beginning of the Composable function
                // but only if this method has @TrackScreenComposable annotation
                if (methodHasTrackScreenComposableAnnotation && methodScreenName != null && !hasInjectedTracking) {
                    hasInjectedTracking = true
                    logDebug(
                        "AnalyticsClassVisitor: Injecting tracking call at start of Composable $methodName " +
                            "with screenName: $methodScreenName",
                    )

                    // Call the injected __injectAnalyticsTracking method (static for composables)
                    delegate.visitMethodInsn(
                        Opcodes.INVOKESTATIC,
                        internalClassName,
                        "__injectAnalyticsTracking",
                        "()V",
                        false,
                    )
                }
            }

            private inner class ComposableAnnotationVisitor(
                private val delegate: AnnotationVisitor?,
            ) : AnnotationVisitor(api, delegate) {
                override fun visit(
                    name: String?,
                    value: Any?,
                ) {
                    when (name) {
                        "value", "screenName" -> {
                            methodScreenName = value as? String
                            screenName = methodScreenName // Also set the class-level screenName
                            PluginLogger.forceDebug("Extracted screenName: $methodScreenName")
                        }

                        else -> value?.let { annotationParameters[name ?: ""] = it }
                    }
                    delegate?.visit(name, value)
                }
            }
        }

        private fun injectTrackingMethod(annotationInfo: TrackingAnnotationInfo) {
            // Inject a method that performs the tracking call
            // Make it static for Composables, private for Activities/Fragments
            val methodAccess =
                if (annotationInfo.annotationType == TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN_COMPOSABLE) {
                    Opcodes.ACC_PRIVATE + Opcodes.ACC_STATIC
                } else {
                    Opcodes.ACC_PRIVATE
                }

            val methodVisitor =
                cv.visitMethod(
                    methodAccess,
                    "__injectAnalyticsTracking",
                    "()V",
                    null,
                    null,
                )

            methodVisitor.visitCode()

            // Generate: ScreenTracking.getManager().logScreenView(screenName, screenClass, parameters)
            methodVisitor.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                "com/shalan/analytics/core/ScreenTracking",
                "getManager",
                "()Lcom/shalan/analytics/core/AnalyticsManager;",
                false,
            )

            // Push screenName parameter
            methodVisitor.visitLdcInsn(annotationInfo.screenName)

            // Push screenClass parameter
            methodVisitor.visitLdcInsn(annotationInfo.screenClass ?: annotationInfo.className)

            // Create and push parameters Map
            generateParametersMap(methodVisitor)

            methodVisitor.visitMethodInsn(
                Opcodes.INVOKEINTERFACE,
                "com/shalan/analytics/core/AnalyticsManager",
                "logScreenView",
                "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V",
                true,
            )

            methodVisitor.visitInsn(Opcodes.RETURN)
            methodVisitor.visitMaxs(6, 1) // Increased stack for array operations
            methodVisitor.visitEnd()

            logDebug("AnalyticsClassVisitor: Successfully injected tracking method")
        }

        private fun generateParametersMap(methodVisitor: MethodVisitor) {
            if (implementsTrackedScreenParamsProvider &&
                (
                    annotationInfo?.annotationType == TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN ||
                        annotationInfo?.annotationType == TrackingAnnotationInfo.AnnotationType.TRACK_SCREEN_COMPOSABLE
                )
            ) {
                // For classes that implement TrackedScreenParamsProvider, directly call the interface method
                // Load 'this' for the interface method call
                methodVisitor.visitVarInsn(Opcodes.ALOAD, 0)

                // Cast this to TrackedScreenParamsProvider
                methodVisitor.visitTypeInsn(Opcodes.CHECKCAST, "com/shalan/analytics/core/TrackedScreenParamsProvider")

                // Call getTrackedScreenParams() method
                methodVisitor.visitMethodInsn(
                    Opcodes.INVOKEINTERFACE,
                    "com/shalan/analytics/core/TrackedScreenParamsProvider",
                    "getTrackedScreenParams",
                    "()Ljava/util/Map;",
                    true,
                )
            } else {
                // Create empty map: Collections.emptyMap()
                methodVisitor.visitMethodInsn(
                    Opcodes.INVOKESTATIC,
                    "java/util/Collections",
                    "emptyMap",
                    "()Ljava/util/Map;",
                    false,
                )
            }
        }

        private fun shouldCollectMethod(
            methodName: String?,
            descriptor: String?,
        ): Boolean {
            // Collect methods that we might want to instrument later
            return when {
                isActivity && methodName == "onCreate" && descriptor == "(Landroid/os/Bundle;)V" -> true
                isFragment && methodName == "onViewCreated" && descriptor == "(Landroid/view/View;Landroid/os/Bundle;)V" -> true
                // Composable functions typically have descriptors with Composer parameter
                isComposableMethod(methodName, descriptor) -> true
                else -> false
            }
        }

        /**
         * Checks if the given class is an Android Activity.
         *
         * @param superName The internal name of the superclass to check
         * @return True if the class extends from a known Activity class, false otherwise
         */
        private fun isActivityClass(superName: String?): Boolean {
            return when (superName) {
                "android/app/Activity",
                "androidx/appcompat/app/AppCompatActivity",
                "androidx/fragment/app/FragmentActivity",
                -> true

                else -> false
            }
        }

        /**
         * Checks if the given class is an Android Fragment.
         *
         * @param superName The internal name of the superclass to check
         * @return True if the class extends from a known Fragment class, false otherwise
         */
        private fun isFragmentClass(superName: String?): Boolean {
            return when (superName) {
                "androidx/fragment/app/Fragment",
                "android/app/Fragment",
                -> true

                else -> false
            }
        }

        /**
         * Checks if the given method is a Jetpack Compose Composable function.
         *
         * Composable functions are identified by having a Composer parameter in their descriptor.
         *
         * @param methodName The name of the method to check
         * @param descriptor The method descriptor containing parameter and return types
         * @return True if the method is a Composable function, false otherwise
         */
        private fun isComposableMethod(
            methodName: String?,
            descriptor: String?,
        ): Boolean {
            // Composable functions have specific signatures with Composer parameter
            val hasComposer = descriptor?.contains("Landroidx/compose/runtime/Composer;") == true

            logDebug("isComposableMethod($methodName, $descriptor) = $hasComposer")

            return hasComposer
        }

        /**
         * Extracts a readable screen name from a class name.
         *
         * This function takes a fully qualified class name and converts it to a screen name
         * by removing the package path and common Android class suffixes.
         *
         * @param className The fully qualified class name (e.g., "com.example.MainActivity")
         * @return A simplified screen name (e.g., "Main" for "MainActivity")
         */
        private fun extractScreenNameFromClassName(className: String): String {
            return className.substringAfterLast('.')
                .removeSuffix("Activity")
                .removeSuffix("Fragment")
                .removeSuffix("Screen")
        }

        private fun extractSimpleClassName(className: String): String {
            return className.substringAfterLast('.')
        }

        private inner class TrackScreenAnnotationVisitor(
            private val delegate: AnnotationVisitor?,
        ) : AnnotationVisitor(api, delegate) {
            override fun visit(
                name: String?,
                value: Any?,
            ) {
                when (name) {
                    "value", "screenName" -> screenName = value as? String
                    "screenClass" -> screenClass = value as? String
                    else -> value?.let { annotationParameters[name ?: ""] = it }
                }
                delegate?.visit(name, value)
            }

            override fun visitEnd() {
                // Create currentAnnotationInfo immediately when annotation processing is complete
                currentAnnotationInfo = createAnnotationInfo()
                logDebug("AnalyticsClassVisitor: Set currentAnnotationInfo: $currentAnnotationInfo")
                delegate?.visitEnd()
            }
        }

        private inner class TrackScreenComposableAnnotationVisitor(
            private val delegate: AnnotationVisitor?,
        ) : AnnotationVisitor(api, delegate) {
            override fun visit(
                name: String?,
                value: Any?,
            ) {
                when (name) {
                    "value", "screenName" -> screenName = value as? String
                    else -> value?.let { annotationParameters[name ?: ""] = it }
                }
                delegate?.visit(name, value)
            }
        }

        private fun logDebug(message: String) {
            if (parameters.debugMode.getOrElse(false)) {
                PluginLogger.debug(message)
            }
        }

        private fun logError(
            message: String,
            throwable: Throwable? = null,
        ) {
            PluginLogger.error(message, throwable)
        }
    }
}
