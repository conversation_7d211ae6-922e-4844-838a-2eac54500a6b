package com.shalan.analytics.plugin.instrumentation

import org.junit.Test
import org.objectweb.asm.ClassReader
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.ClassWriter
import org.objectweb.asm.Label
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.Opcodes
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Integration tests for TrackMethodVisitor that verify complete bytecode transformation
 * including @Track and @Param annotation processing, method instrumentation, and
 * configuration-based behavior.
 */
class TrackMethodVisitorIntegrationTest {

    @Test
    fun `TrackMethodVisitor instruments simple method with Track annotation`() {
        val originalClass = createClassWithTrackMethod(
            className = "com/test/TestClass",
            methodName = "trackableMethod",
            eventName = "button_click",
            includeGlobalParams = true
        )

        val instrumentedClass = instrumentClass(originalClass, getDefaultConfiguration())

        // Verify the instrumented class contains MethodTrackingManager.track call
        val calls = extractMethodCalls(instrumentedClass, "trackableMethod")
        assertTrue(calls.any { it.owner == "com/shalan/analytics/core/MethodTrackingManager" })
        assertTrue(calls.any { it.name == "track" })
    }

    @Test
    fun `TrackMethodVisitor processes Param annotations correctly`() {
        val originalClass = createClassWithTrackMethodAndParams(
            className = "com/test/TestClass",
            methodName = "methodWithParams",
            eventName = "user_action",
            paramNames = listOf("userId", "actionType")
        )

        val instrumentedClass = instrumentClass(originalClass, getDefaultConfiguration())

        // Verify parameter map creation
        val bytecode = extractMethodBytecode(instrumentedClass, "methodWithParams")
        
        // Should contain HashMap creation for parameters
        assertTrue(bytecode.contains("java/util/HashMap"))
        
        // Should contain parameter name strings
        assertTrue(bytecode.contains("userId"))
        assertTrue(bytecode.contains("actionType"))
        
        // Should contain Map.put calls for each parameter
        val putCalls = extractMethodCalls(instrumentedClass, "methodWithParams")
            .filter { it.name == "put" && it.owner == "java/util/Map" }
        assertEquals(2, putCalls.size) // One for each parameter
    }

    @Test
    fun `TrackMethodVisitor respects maxParametersPerMethod configuration`() {
        val originalClass = createClassWithTrackMethodAndParams(
            className = "com/test/TestClass",
            methodName = "methodWithManyParams",
            eventName = "complex_action",
            paramNames = listOf("param1", "param2", "param3", "param4", "param5")
        )

        val config = getDefaultConfiguration().copy(
            maxParametersPerMethod = 3 // Limit to 3 parameters
        )
        val instrumentedClass = instrumentClass(originalClass, config)

        // Should only process first 3 parameters
        val putCalls = extractMethodCalls(instrumentedClass, "methodWithManyParams")
            .filter { it.name == "put" && it.owner == "java/util/Map" }
        assertTrue(putCalls.size <= 3) // Should be limited by configuration
    }

    @Test
    fun `TrackMethodVisitor skips excluded methods`() {
        val originalClass = createClassWithTrackMethod(
            className = "com/test/TestClass",
            methodName = "excludedMethod",
            eventName = "excluded_event",
            includeGlobalParams = true
        )

        val config = getDefaultConfiguration().copy(
            excludeMethods = setOf("excludedMethod")
        )
        val instrumentedClass = instrumentClass(originalClass, config)

        // Should not contain MethodTrackingManager calls since method is excluded
        val calls = extractMethodCalls(instrumentedClass, "excludedMethod")
        assertFalse(calls.any { it.owner == "com/shalan/analytics/core/MethodTrackingManager" })
    }

    @Test
    fun `TrackMethodVisitor skips instrumentation when method tracking disabled`() {
        val originalClass = createClassWithTrackMethod(
            className = "com/test/TestClass",
            methodName = "trackableMethod",
            eventName = "button_click",
            includeGlobalParams = true
        )

        val config = getDefaultConfiguration().copy(
            methodTrackingEnabled = false
        )
        val instrumentedClass = instrumentClass(originalClass, config)

        // Should not contain MethodTrackingManager calls when disabled
        val calls = extractMethodCalls(instrumentedClass, "trackableMethod")
        assertFalse(calls.any { it.owner == "com/shalan/analytics/core/MethodTrackingManager" })
    }

    @Test
    fun `TrackMethodVisitor handles execution timing when enabled`() {
        val originalClass = createClassWithTrackMethod(
            className = "com/test/TestClass",
            methodName = "timedMethod",
            eventName = "timed_action",
            includeGlobalParams = true
        )

        val config = getDefaultConfiguration().copy(
            enableExecutionTiming = true
        )
        val instrumentedClass = instrumentClass(originalClass, config)

        // Should contain execution_time_ms parameter when timing is enabled
        val bytecode = extractMethodBytecode(instrumentedClass, "timedMethod")
        assertTrue(bytecode.contains("execution_time_ms"))
    }

    @Test
    fun `TrackMethodVisitor handles primitive parameter types correctly`() {
        val originalClass = createClassWithPrimitiveParams(
            className = "com/test/TestClass",
            methodName = "primitiveMethod",
            eventName = "primitive_test"
        )

        val instrumentedClass = instrumentClass(originalClass, getDefaultConfiguration())

        // Verify boxing operations for primitives
        val calls = extractMethodCalls(instrumentedClass, "primitiveMethod")
        
        // Should contain boxing calls for primitives
        assertTrue(calls.any { it.owner == "java/lang/Integer" && it.name == "valueOf" })
        assertTrue(calls.any { it.owner == "java/lang/Boolean" && it.name == "valueOf" })
        assertTrue(calls.any { it.owner == "java/lang/Float" && it.name == "valueOf" })
    }

    @Test
    fun `TrackMethodVisitor maintains method signature and behavior`() {
        val originalClass = createClassWithReturnValue(
            className = "com/test/TestClass",
            methodName = "businessMethod",
            eventName = "business_action",
            returnValue = 42
        )

        val instrumentedClass = instrumentClass(originalClass, getDefaultConfiguration())

        // Load and test the instrumented class
        val loadedClass = loadClass(instrumentedClass, "com.test.TestClass")
        val instance = loadedClass.getDeclaredConstructor().newInstance()
        val method = loadedClass.getDeclaredMethod("businessMethod", Int::class.java)
        
        // Method should still return the correct value after instrumentation
        val result = method.invoke(instance, 10)
        assertEquals(42, result)
    }

    @Test
    fun `TrackMethodVisitor handles static methods correctly`() {
        val originalClass = createClassWithStaticTrackMethod(
            className = "com/test/TestClass",
            methodName = "staticTrackableMethod",
            eventName = "static_action"
        )

        val instrumentedClass = instrumentClass(originalClass, getDefaultConfiguration())

        // Should handle static methods without 'this' parameter
        val calls = extractMethodCalls(instrumentedClass, "staticTrackableMethod")
        assertTrue(calls.any { it.owner == "com/shalan/analytics/core/MethodTrackingManager" })
        assertTrue(calls.any { it.name == "track" })
    }

    // Helper methods for creating test classes and extracting information

    private fun createClassWithTrackMethod(
        className: String,
        methodName: String,
        eventName: String,
        includeGlobalParams: Boolean
    ): ByteArray {
        val classWriter = ClassWriter(ClassWriter.COMPUTE_FRAMES)
        classWriter.visit(
            Opcodes.V1_8,
            Opcodes.ACC_PUBLIC,
            className,
            null,
            "java/lang/Object",
            null
        )

        // Add constructor
        addDefaultConstructor(classWriter)

        // Add method with @Track annotation
        val methodVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC,
            methodName,
            "()V",
            null,
            null
        )

        // Add @Track annotation
        val trackAnnotation = methodVisitor.visitAnnotation(
            "Lcom/shalan/analytics/annotation/Track;",
            true
        )
        trackAnnotation.visit("eventName", eventName)
        trackAnnotation.visit("includeGlobalParams", includeGlobalParams)
        trackAnnotation.visitEnd()

        methodVisitor.visitCode()
        methodVisitor.visitInsn(Opcodes.RETURN)
        methodVisitor.visitMaxs(1, 1)
        methodVisitor.visitEnd()

        classWriter.visitEnd()
        return classWriter.toByteArray()
    }

    private fun createClassWithTrackMethodAndParams(
        className: String,
        methodName: String,
        eventName: String,
        paramNames: List<String>
    ): ByteArray {
        val classWriter = ClassWriter(ClassWriter.COMPUTE_FRAMES)
        classWriter.visit(
            Opcodes.V1_8,
            Opcodes.ACC_PUBLIC,
            className,
            null,
            "java/lang/Object",
            null
        )

        addDefaultConstructor(classWriter)

        // Create method descriptor with String parameters
        val paramDescriptor = "(" + "Ljava/lang/String;".repeat(paramNames.size) + ")V"
        
        val methodVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC,
            methodName,
            paramDescriptor,
            null,
            null
        )

        // Add @Track annotation
        val trackAnnotation = methodVisitor.visitAnnotation(
            "Lcom/shalan/analytics/annotation/Track;",
            true
        )
        trackAnnotation.visit("eventName", eventName)
        trackAnnotation.visit("includeGlobalParams", true)
        trackAnnotation.visitEnd()

        // Add @Param annotations for each parameter
        paramNames.forEachIndexed { index, paramName ->
            val paramAnnotation = methodVisitor.visitParameterAnnotation(
                index,
                "Lcom/shalan/analytics/annotation/Param;",
                true
            )
            paramAnnotation.visit("name", paramName)
            paramAnnotation.visitEnd()
        }

        methodVisitor.visitCode()
        methodVisitor.visitInsn(Opcodes.RETURN)
        methodVisitor.visitMaxs(1, paramNames.size + 1)
        methodVisitor.visitEnd()

        classWriter.visitEnd()
        return classWriter.toByteArray()
    }

    private fun createClassWithPrimitiveParams(
        className: String,
        methodName: String,
        eventName: String
    ): ByteArray {
        val classWriter = ClassWriter(ClassWriter.COMPUTE_FRAMES)
        classWriter.visit(
            Opcodes.V1_8,
            Opcodes.ACC_PUBLIC,
            className,
            null,
            "java/lang/Object",
            null
        )

        addDefaultConstructor(classWriter)

        // Method with int, boolean, float parameters
        val methodVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC,
            methodName,
            "(IZF)V",
            null,
            null
        )

        // Add @Track annotation
        val trackAnnotation = methodVisitor.visitAnnotation(
            "Lcom/shalan/analytics/annotation/Track;",
            true
        )
        trackAnnotation.visit("eventName", eventName)
        trackAnnotation.visitEnd()

        // Add @Param annotations for each parameter
        val paramNames = listOf("intParam", "boolParam", "floatParam")
        paramNames.forEachIndexed { index, paramName ->
            val paramAnnotation = methodVisitor.visitParameterAnnotation(
                index,
                "Lcom/shalan/analytics/annotation/Param;",
                true
            )
            paramAnnotation.visit("name", paramName)
            paramAnnotation.visitEnd()
        }

        methodVisitor.visitCode()
        methodVisitor.visitInsn(Opcodes.RETURN)
        methodVisitor.visitMaxs(1, 4)
        methodVisitor.visitEnd()

        classWriter.visitEnd()
        return classWriter.toByteArray()
    }

    private fun createClassWithReturnValue(
        className: String,
        methodName: String,
        eventName: String,
        returnValue: Int
    ): ByteArray {
        val classWriter = ClassWriter(ClassWriter.COMPUTE_FRAMES)
        classWriter.visit(
            Opcodes.V1_8,
            Opcodes.ACC_PUBLIC,
            className,
            null,
            "java/lang/Object",
            null
        )

        addDefaultConstructor(classWriter)

        // Method that takes an int parameter and returns an int
        val methodVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC,
            methodName,
            "(I)I",
            null,
            null
        )

        // Add @Track annotation
        val trackAnnotation = methodVisitor.visitAnnotation(
            "Lcom/shalan/analytics/annotation/Track;",
            true
        )
        trackAnnotation.visit("eventName", eventName)
        trackAnnotation.visitEnd()

        methodVisitor.visitCode()
        methodVisitor.visitLdcInsn(returnValue)
        methodVisitor.visitInsn(Opcodes.IRETURN)
        methodVisitor.visitMaxs(2, 2)
        methodVisitor.visitEnd()

        classWriter.visitEnd()
        return classWriter.toByteArray()
    }

    private fun createClassWithStaticTrackMethod(
        className: String,
        methodName: String,
        eventName: String
    ): ByteArray {
        val classWriter = ClassWriter(ClassWriter.COMPUTE_FRAMES)
        classWriter.visit(
            Opcodes.V1_8,
            Opcodes.ACC_PUBLIC,
            className,
            null,
            "java/lang/Object",
            null
        )

        addDefaultConstructor(classWriter)

        // Static method with @Track annotation
        val methodVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC or Opcodes.ACC_STATIC,
            methodName,
            "()V",
            null,
            null
        )

        // Add @Track annotation
        val trackAnnotation = methodVisitor.visitAnnotation(
            "Lcom/shalan/analytics/annotation/Track;",
            true
        )
        trackAnnotation.visit("eventName", eventName)
        trackAnnotation.visitEnd()

        methodVisitor.visitCode()
        methodVisitor.visitInsn(Opcodes.RETURN)
        methodVisitor.visitMaxs(1, 0)
        methodVisitor.visitEnd()

        classWriter.visitEnd()
        return classWriter.toByteArray()
    }

    private fun addDefaultConstructor(classWriter: ClassWriter) {
        val constructorVisitor = classWriter.visitMethod(
            Opcodes.ACC_PUBLIC,
            "<init>",
            "()V",
            null,
            null
        )
        constructorVisitor.visitCode()
        constructorVisitor.visitVarInsn(Opcodes.ALOAD, 0)
        constructorVisitor.visitMethodInsn(
            Opcodes.INVOKESPECIAL,
            "java/lang/Object",
            "<init>",
            "()V",
            false
        )
        constructorVisitor.visitInsn(Opcodes.RETURN)
        constructorVisitor.visitMaxs(1, 1)
        constructorVisitor.visitEnd()
    }

    private fun instrumentClass(
        classBytes: ByteArray,
        config: TestConfiguration
    ): ByteArray {
        val classReader = ClassReader(classBytes)
        val classWriter = ClassWriter(classReader, ClassWriter.COMPUTE_FRAMES)

        val instrumentingVisitor = object : ClassVisitor(Opcodes.ASM9, classWriter) {
            override fun visitMethod(
                access: Int,
                name: String?,
                descriptor: String?,
                signature: String?,
                exceptions: Array<out String>?
            ): MethodVisitor? {
                val delegate = super.visitMethod(access, name, descriptor, signature, exceptions)
                
                if (name != null && descriptor != null && name != "<init>") {
                    return TrackMethodVisitor(
                        api = Opcodes.ASM9,
                        delegate = delegate,
                        methodName = name,
                        methodDescriptor = descriptor,
                        className = "test-class",
                        access = access,
                        methodTrackingEnabled = config.methodTrackingEnabled,
                        maxParametersPerMethod = config.maxParametersPerMethod,
                        enableExecutionTiming = config.enableExecutionTiming,
                        excludeMethods = config.excludeMethods
                    )
                }
                return delegate
            }
        }

        classReader.accept(instrumentingVisitor, ClassReader.EXPAND_FRAMES)
        return classWriter.toByteArray()
    }

    private fun extractMethodCalls(classBytes: ByteArray, methodName: String): List<MethodCall> {
        val calls = mutableListOf<MethodCall>()
        val classReader = ClassReader(classBytes)

        classReader.accept(object : ClassVisitor(Opcodes.ASM9) {
            override fun visitMethod(
                access: Int,
                name: String?,
                descriptor: String?,
                signature: String?,
                exceptions: Array<out String>?
            ): MethodVisitor? {
                if (name == methodName) {
                    return object : MethodVisitor(Opcodes.ASM9) {
                        override fun visitMethodInsn(
                            opcode: Int,
                            owner: String?,
                            name: String?,
                            descriptor: String?,
                            isInterface: Boolean
                        ) {
                            if (owner != null && name != null) {
                                calls.add(MethodCall(opcode, owner, name, descriptor ?: ""))
                            }
                            super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
                        }
                    }
                }
                return null
            }
        }, ClassReader.SKIP_DEBUG)

        return calls
    }

    private fun extractMethodBytecode(classBytes: ByteArray, methodName: String): String {
        val bytecodeElements = mutableListOf<String>()
        val classReader = ClassReader(classBytes)

        classReader.accept(object : ClassVisitor(Opcodes.ASM9) {
            override fun visitMethod(
                access: Int,
                name: String?,
                descriptor: String?,
                signature: String?,
                exceptions: Array<out String>?
            ): MethodVisitor? {
                if (name == methodName) {
                    return object : MethodVisitor(Opcodes.ASM9) {
                        override fun visitLdcInsn(value: Any?) {
                            bytecodeElements.add(value?.toString() ?: "null")
                            super.visitLdcInsn(value)
                        }

                        override fun visitTypeInsn(opcode: Int, type: String?) {
                            type?.let { bytecodeElements.add(it) }
                            super.visitTypeInsn(opcode, type)
                        }

                        override fun visitMethodInsn(
                            opcode: Int,
                            owner: String?,
                            name: String?,
                            descriptor: String?,
                            isInterface: Boolean
                        ) {
                            owner?.let { bytecodeElements.add(it) }
                            name?.let { bytecodeElements.add(it) }
                            super.visitMethodInsn(opcode, owner, name, descriptor, isInterface)
                        }
                    }
                }
                return null
            }
        }, ClassReader.SKIP_DEBUG)

        return bytecodeElements.joinToString(" ")
    }

    private fun loadClass(classBytes: ByteArray, className: String): Class<*> {
        val classLoader = object : ClassLoader() {
            fun defineClass(name: String, bytes: ByteArray): Class<*> {
                return defineClass(name, bytes, 0, bytes.size)
            }
        }
        return classLoader.defineClass(className, classBytes)
    }

    private fun getDefaultConfiguration() = TestConfiguration(
        methodTrackingEnabled = true,
        maxParametersPerMethod = 10,
        enableExecutionTiming = false,
        excludeMethods = emptySet()
    )

    // Helper data classes
    data class MethodCall(
        val opcode: Int,
        val owner: String,
        val name: String,
        val descriptor: String
    )

    data class TestConfiguration(
        val methodTrackingEnabled: Boolean,
        val maxParametersPerMethod: Int,
        val enableExecutionTiming: Boolean,
        val excludeMethods: Set<String>
    )
}