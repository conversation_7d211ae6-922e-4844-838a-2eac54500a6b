# Method-Level Tracking Implementation Plan

## Overview

This document outlines the detailed implementation plan for adding method-level tracking capabilities to the analytics-annotation library. The feature will enable developers to annotate methods with `@Track` and parameters with `@Param` for automatic analytics event logging.

## GitHub Issue Reference

**Issue**: [Method-Level Tracking with Parameter Annotation #2](https://github.com/sh3lan93/analytics-annotation/issues/2)

## Current Architecture Analysis

### Existing Components
- **Screen Tracking**: `@TrackScreen` annotation for Activities/Fragments/Composables
- **Analytics Manager**: Interface-based provider pattern with multiple analytics services
- **Build-time Instrumentation**: ASM-based bytecode transformation via Gradle plugin
- **Runtime Lifecycle Hooks**: Activity/Fragment lifecycle callbacks for automatic tracking

### Current Module Structure
```
:annotation     - @TrackScreen annotation definitions
:core          - Analytics managers, providers, and lifecycle callbacks
:compose       - Jetpack Compose integration (@TrackScreenComposable)
:app           - Sample application
:plugin        - Gradle plugin for ASM bytecode instrumentation
```

## Proposed Architecture

### New Annotations

#### 1. `@Track` Annotation
**Location**: `:annotation` module  
**File**: `com.shalan.analytics.annotation.Track`

```kotlin
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class Track(
    val eventName: String,
    val includeGlobalParams: Boolean = true
)
```

#### 2. `@Param` Annotation
**Location**: `:annotation` module  
**File**: `com.shalan.analytics.annotation.Param`

```kotlin
@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class Param(
    val name: String = ""
)
```

### Core Implementation Components

#### 1. Method Interceptor Infrastructure
**Location**: `:core` module

##### A. `MethodTrackingInterceptor` Interface
```kotlin
interface MethodTrackingInterceptor {
    fun intercept(
        eventName: String,
        parameters: Map<String, Any?>,
        includeGlobalParams: Boolean
    )
}
```

##### B. `MethodTrackingInterceptorImpl` Class
- Integrates with existing `AnalyticsManager`
- Handles parameter serialization
- Manages global parameter inclusion
- Implements error handling and fallback mechanisms

#### 2. Parameter Processing
**Location**: `:core` module

##### A. `ParameterSerializer` Interface
```kotlin
interface ParameterSerializer {
    fun serialize(value: Any?): Any?
    fun canSerialize(type: Class<*>): Boolean
}
```

##### B. Built-in Serializers
- `PrimitiveParameterSerializer` - Handles String, Int, Long, Boolean, Float, Double
- `SerializableParameterSerializer` - Handles Serializable objects
- `JsonParameterSerializer` - Optional JSON serialization for complex objects

#### 3. Reflection Cache
**Location**: `:core` module

##### A. `MethodMetadataCache` Class
- Caches method annotation information
- Stores parameter names and types
- Implements lazy loading and thread-safe access
- Prevents repeated reflection calls

### Build-Time Instrumentation Extensions

#### 1. ASM Visitor Enhancements
**Location**: `:plugin` module  
**File**: `AnalyticsClassVisitorFactory.kt` (existing)

##### A. New Method Visitor: `TrackMethodVisitor`
- Detects `@Track` annotations on methods
- Identifies `@Param` annotations on parameters
- Injects tracking code at method entry/exit points
- Handles method signature analysis

##### B. Enhanced `AnalyticsClassVisitor`
- Extends existing visitor to support method-level annotations
- Adds method tracking capabilities alongside screen tracking
- Maintains backward compatibility

#### 2. Code Generation Strategy

##### A. Method Wrapper Injection
```kotlin
// Original method
@Track(eventName = "user_login")
fun login(@Param("username") username: String, password: String) {
    // Original implementation
}

// Generated wrapper (conceptual)
fun login(username: String, password: String) {
    __injectMethodTracking("user_login", mapOf("username" to username))
    // Original implementation
}
```

##### B. Generated Helper Methods
- `__injectMethodTracking(eventName: String, parameters: Map<String, Any?>)`
- `__extractParameterValue(parameter: Any?): Any?`
- `__handleTrackingError(throwable: Throwable)`

### Runtime Components

#### 1. Method Tracking Manager
**Location**: `:core` module

##### A. `MethodTrackingManager` Class
- Singleton pattern following existing `ScreenTracking` approach
- Manages method interceptors
- Coordinates with existing `AnalyticsManager`
- Handles configuration and lifecycle

#### 2. Error Handling System
**Location**: `:core` module

##### A. `TrackingErrorHandler` Interface
```kotlin
interface TrackingErrorHandler {
    fun handleSerializationError(parameter: Any?, throwable: Throwable)
    fun handleInfiniteRecursion(methodName: String)
    fun handleProviderFailure(eventName: String, throwable: Throwable)
}
```

##### B. Built-in Error Handlers
- `LoggingErrorHandler` - Logs errors without crashing
- `DebugErrorHandler` - Detailed error reporting for development
- `SilentErrorHandler` - Suppresses all errors in production

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)
1. **Annotation Definitions**
   - Create `@Track` and `@Param` annotations in `:annotation` module
   - Add comprehensive KDoc documentation
   - Include usage examples

2. **Parameter Serialization System**
   - Implement `ParameterSerializer` interface and built-in implementations
   - Create serialization registry for extensibility
   - Add comprehensive unit tests

3. **Method Metadata Cache**
   - Implement reflection caching system
   - Add thread-safety mechanisms
   - Performance benchmarking and optimization

### Phase 2: Build-Time Instrumentation (Week 3-4)
1. **ASM Visitor Extensions**
   - Extend existing `AnalyticsClassVisitor` for method support
   - Implement `TrackMethodVisitor` for method-level instrumentation
   - Add method signature analysis capabilities

2. **Code Generation Logic**
   - Implement method wrapper injection strategy
   - Generate helper methods for tracking calls
   - Ensure compatibility with existing screen tracking

3. **Plugin Configuration**
   - Extend plugin configuration for method tracking options
   - Add include/exclude patterns for method tracking
   - Implement granular control settings

### Phase 3: Runtime Integration (Week 5)
1. **Method Tracking Manager**
   - Implement singleton pattern consistent with existing architecture
   - Integrate with existing `AnalyticsManager`
   - Add configuration DSL support

2. **Error Handling Implementation**
   - Create comprehensive error handling system
   - Implement fallback mechanisms
   - Add configurable error reporting levels

### Phase 4: Testing & Validation (Week 6)
1. **Unit Tests**
   - Comprehensive test coverage for all new components
   - Mock-based testing for external dependencies
   - Edge case validation (null parameters, serialization failures)

2. **Integration Tests**
   - End-to-end testing with sample applications
   - Performance benchmarking
   - Memory leak detection

3. **Sample Application Updates**
   - Add method tracking examples to `:app` module
   - Demonstrate various use cases and best practices
   - Include error handling scenarios

### Phase 5: Documentation & Polish (Week 7)
1. **Documentation**
   - Update README with method tracking usage
   - Create comprehensive API documentation
   - Add migration guide from manual tracking

2. **Performance Optimization**
   - Profile and optimize hot paths
   - Minimize reflection overhead
   - Optimize generated bytecode

## Technical Considerations

### 1. Performance Impact
- **Reflection Caching**: Minimize reflection calls through intelligent caching
- **Lazy Initialization**: Load metadata only when methods are actually called
- **Bytecode Optimization**: Generate efficient instrumentation code

### 2. Memory Management
- **Weak References**: Use weak references in caches to prevent memory leaks
- **Parameter Cleanup**: Ensure parameter maps are GC-eligible after tracking
- **Cache Size Limits**: Implement LRU eviction for metadata caches

### 3. Thread Safety
- **Concurrent Access**: All caches and managers must be thread-safe
- **Atomic Operations**: Use atomic operations for counter and state updates
- **Synchronized Blocks**: Minimal and well-scoped synchronization

### 4. Error Resilience
- **Graceful Degradation**: Never crash the host application
- **Fallback Mechanisms**: Provide sensible defaults when tracking fails
- **Circuit Breaker Pattern**: Temporarily disable tracking on repeated failures

### 5. Compatibility
- **Android API Levels**: Support minimum API level 21 (current library requirement)
- **Kotlin Versions**: Compatible with Kotlin 1.8+ (current project setting)
- **Build Tools**: Compatible with AGP 8.0+ (current project setting)

## Configuration & Usage

### 1. DSL Configuration Extension
```kotlin
ScreenTracking.initialize(application) {
    debugMode = BuildConfig.DEBUG
    providers.add(LogcatAnalyticsProvider())
    
    // New method tracking configuration
    methodTracking {
        enabled = true
        includeGlobalParamsByDefault = true
        errorHandler = LoggingErrorHandler()
        maxCacheSize = 1000
        
        serializers {
            add(PrimitiveParameterSerializer())
            add(SerializableParameterSerializer())
        }
    }
}
```

### 2. Usage Examples
```kotlin
class UserService {
    
    @Track(eventName = "user_login_attempt")
    fun login(
        @Param("username") username: String,
        @Param password: String,  // name inferred from parameter
        rememberMe: Boolean  // not tracked (no @Param)
    ): LoginResult {
        // Implementation
    }
    
    @Track(eventName = "user_profile_update", includeGlobalParams = false)
    fun updateProfile(
        @Param("user_id") userId: String,
        @Param("changes") profileData: UserProfile
    ) {
        // Implementation
    }
}
```

### 3. Gradle Plugin Configuration
```kotlin
analyticsAnnotation {
    enabled = true
    debugMode = false
    trackActivities = true
    trackFragments = true
    trackComposables = true
    
    // New method tracking options
    trackMethods = true
    includePackages.addAll("com.yourpackage.services", "com.yourpackage.repositories")
    excludePackages.addAll("com.yourpackage.internal")
}
```

## Testing Strategy

### 1. Unit Tests
- **Annotation Processing**: Verify correct parsing of `@Track` and `@Param`
- **Parameter Serialization**: Test all serializer implementations
- **Error Handling**: Validate graceful failure scenarios
- **Cache Behavior**: Test cache hit/miss ratios and eviction

### 2. Integration Tests
- **Build-Time Instrumentation**: Verify correct bytecode generation
- **Runtime Execution**: End-to-end tracking verification
- **Performance Tests**: Measure overhead and memory usage
- **Android Tests**: Device/emulator testing with real analytics providers

### 3. Sample Applications
- **Business Logic Tracking**: Service layer method tracking
- **User Interaction Tracking**: Button clicks, form submissions
- **Error Scenarios**: Network failures, invalid data handling
- **Performance Scenarios**: High-frequency method calls

## Deliverables

### 1. Code Artifacts
- [ ] New annotation classes (`@Track`, `@Param`)
- [ ] Parameter serialization system with built-in serializers
- [ ] Method metadata cache with thread-safe implementation
- [ ] Enhanced ASM visitors for method instrumentation
- [ ] Method tracking manager and runtime components
- [ ] Comprehensive error handling system
- [ ] Updated sample application with method tracking examples

### 2. Documentation
- [ ] Updated README with method tracking usage
- [ ] KDoc documentation for all public APIs
- [ ] Migration guide from manual to annotation-based tracking
- [ ] Performance characteristics and best practices
- [ ] Troubleshooting guide for common issues

### 3. Testing
- [ ] Unit test suite with >90% coverage
- [ ] Integration tests for build-time instrumentation
- [ ] Performance benchmarks and regression tests
- [ ] Android device testing across API levels
- [ ] Memory leak detection and prevention

### 4. Configuration
- [ ] DSL extensions for method tracking configuration
- [ ] Gradle plugin parameter extensions
- [ ] Default configuration templates
- [ ] Environment-specific configuration examples

## Success Metrics

### 1. Functional Requirements
- [ ] Methods annotated with `@Track` automatically log events
- [ ] Parameters annotated with `@Param` are captured accurately
- [ ] Global parameters are included when configured
- [ ] Error scenarios are handled gracefully without crashes
- [ ] Serialization works for all supported parameter types

### 2. Performance Requirements
- [ ] <10ms overhead per tracked method call
- [ ] <50MB additional memory usage for typical applications
- [ ] <5% increase in APK size due to instrumentation
- [ ] Cache hit ratio >90% for repeated method calls

### 3. Quality Requirements
- [ ] Zero crashes caused by method tracking
- [ ] Backward compatibility with existing screen tracking
- [ ] Clean integration with existing analytics providers
- [ ] Comprehensive error reporting and debugging support

This comprehensive plan provides a roadmap for implementing method-level tracking while maintaining the library's core principles of simplicity, reliability, and performance.