package com.shalan.analytics.core

/**
 * Singleton manager for handling method tracking events generated by ASM instrumentation.
 * This class receives direct calls from ASM-injected code and dispatches events to analytics providers.
 *
 * ASM-generated code calls this manager like:
 * ```
 * MethodTrackingManager.track("user_action", mapOf("userId" to userId), true)
 * ```
 */
object MethodTrackingManager {
    private val serializers = mutableListOf<ParameterSerializer>()
    private var analyticsManager: AnalyticsManager? = null
    private var errorHandler: ((Throwable) -> Unit)? = null

    /**
     * Initializes the method tracking manager with required dependencies.
     * Should be called during application initialization.
     */
    fun initialize(
        analyticsManager: AnalyticsManager,
        errorHandler: ((Throwable) -> Unit)? = null,
    ) {
        this.analyticsManager = analyticsManager
        this.errorHandler = errorHandler

        // Register default parameter serializers
        if (serializers.isEmpty()) {
            serializers.add(PrimitiveParameterSerializer())
            serializers.add(SerializableParameterSerializer())
        }
    }

    /**
     * Tracks a method execution event. This method is called by ASM-generated code.
     *
     * @param eventName The name of the event to track
     * @param parameters Map of parameter names to values
     * @param includeGlobalParams Whether to include global parameters
     */
    @JvmStatic
    fun track(
        eventName: String,
        parameters: Map<String, Any?>,
        includeGlobalParams: Boolean = true,
    ) {
        try {
            val manager = analyticsManager ?: return

            // Serialize parameters
            val serializedParams = mutableMapOf<String, Any>()

            parameters.forEach { (key, value) ->
                val serializedValue = serializeParameter(value)
                if (serializedValue != null) {
                    serializedParams[key] = serializedValue
                }
            }

            // Track the event
            manager.logEvent(eventName, serializedParams, includeGlobalParams)
        } catch (e: Throwable) {
            errorHandler?.invoke(e)
            // Silently ignore errors if no error handler is provided
            // Analytics should never crash the app
        }
    }

    /**
     * Adds a custom parameter serializer.
     * Serializers are checked in reverse order of addition (last added = highest priority).
     */
    fun addParameterSerializer(serializer: ParameterSerializer) {
        synchronized(serializers) {
            serializers.add(serializer)
            // Sort by priority (highest first)
            serializers.sortByDescending { it.getPriority() }
        }
    }

    /**
     * Removes all parameter serializers. Mainly for testing.
     */
    fun clearParameterSerializers() {
        synchronized(serializers) {
            serializers.clear()
        }
    }

    private fun serializeParameter(value: Any?): Any? {
        if (value == null) return null

        val valueType = value.javaClass

        synchronized(serializers) {
            for (serializer in serializers) {
                if (serializer.canSerialize(valueType)) {
                    return serializer.serialize(value, valueType)
                }
            }
        }

        // Fallback to toString if no serializer can handle the type
        return value.toString()
    }
}
