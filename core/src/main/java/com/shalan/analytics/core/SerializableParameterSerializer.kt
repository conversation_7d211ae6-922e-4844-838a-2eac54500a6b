package com.shalan.analytics.core

import java.io.Serializable

/**
 * Parameter serializer for objects implementing Serializable interface.
 * Handles collections, maps, and custom serializable objects by converting them
 * to string representations suitable for analytics.
 */
class SerializableParameterSerializer : ParameterSerializer {
    override fun canSerialize(parameterType: Class<*>): <PERSON><PERSON>an {
        return Serializable::class.java.isAssignableFrom(parameterType)
    }

    override fun serialize(
        value: Any?,
        parameterType: Class<*>,
    ): Any? {
        if (value == null) return null

        return try {
            when (value) {
                is Collection<*> -> {
                    if (value.isEmpty()) {
                        "[]"
                    } else {
                        val size = value.size
                        val firstItem = value.firstOrNull()
                        "Collection[size=$size, type=${firstItem?.javaClass?.simpleName ?: "null"}]"
                    }
                }
                is Map<*, *> -> {
                    if (value.isEmpty()) {
                        "{}"
                    } else {
                        val size = value.size
                        val firstEntry = value.entries.firstOrNull()
                        val keyType = firstEntry?.key?.javaClass?.simpleName ?: "null"
                        val valueType = firstEntry?.value?.javaClass?.simpleName ?: "null"
                        "Map[size=$size, keyType=$keyType, valueType=$valueType]"
                    }
                }
                is Array<*> -> {
                    val size = value.size
                    val componentType = value.javaClass.componentType.simpleName
                    "Array[size=$size, type=$componentType]"
                }
                else -> {
                    // For other Serializable objects, use toString or class info
                    val className = value.javaClass.simpleName
                    val toString = value.toString()

                    // If toString seems meaningful (not just object reference), use it
                    if (!toString.matches(Regex(".*@[a-fA-F0-9]+$"))) {
                        toString
                    } else {
                        className
                    }
                }
            }
        } catch (e: Exception) {
            // Fallback to class name if serialization fails
            value.javaClass.simpleName
        }
    }

    override fun getPriority(): Int = 50 // Medium priority, after primitives
}
