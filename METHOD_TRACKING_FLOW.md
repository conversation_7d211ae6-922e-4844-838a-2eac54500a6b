# Method Tracking ASM Implementation Flow

This document explains the detailed flow of how the ASM method tracking implementation works, from build-time instrumentation to runtime execution.

## **High-Level Architecture**

The flow goes: **Build-time ASM → Runtime Manager → Analytics Providers**

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│   Build Time    │    │     Runtime          │    │   Analytics     │
│   ASM Visitor   │────│ MethodTrackingManager│────│   Providers     │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
```

## **Build-Time: ASM Instrumentation Flow**

### **1. Class Processing Entry Point**
```kotlin
// AnalyticsClassVisitorFactory.visitMethod()
override fun visitMethod(...): MethodVisitor {
    // ALL methods get wrapped with TrackMethodVisitor
    return TrackMethodVisitor(api, methodVisitor, name, descriptor, className)
}
```

### **2. TrackMethodVisitor Annotation Detection**
```kotlin
// TrackMethodVisitor.visitAnnotation() 
override fun visitAnnotation(descriptor: String?, visible: Boolean): AnnotationVisitor? {
    return when (descriptor) {
        "Lcom/shalan/analytics/annotation/Track;" -> {
            // Create TrackAnnotationVisitor to extract @Track properties
            TrackAnnotationVisitor(delegate.visitAnnotation(descriptor, visible))
        }
        else -> delegate.visitAnnotation(descriptor, visible)
    }
}
```

### **3. Annotation Data Extraction**
```kotlin
// TrackAnnotationVisitor extracts:
private inner class TrackAnnotationVisitor : AnnotationVisitor {
    override fun visit(name: String?, value: Any?) {
        when (name) {
            "eventName" -> eventName = value as String    // "user_login"
            "includeGlobalParams" -> includeGlobalParams = value as Boolean // true
        }
    }
    
    override fun visitEnd() {
        // Creates MethodTrackInfo with collected data
        trackAnnotationInfo = MethodTrackInfo(eventName, includeGlobalParams, paramInfos)
    }
}
```

### **4. Parameter Annotation Detection**
```kotlin
// TrackMethodVisitor.visitParameterAnnotation()
override fun visitParameterAnnotation(parameter: Int, descriptor: String?, visible: Boolean): AnnotationVisitor? {
    return when (descriptor) {
        "Lcom/shalan/analytics/annotation/Param;" -> {
            // Extract @Param(name="userId") from parameter index 0
            ParamAnnotationVisitor(parameter, delegate.visitParameterAnnotation(...))
        }
    }
}
```

### **5. Code Injection at Method Entry**
```kotlin
// TrackMethodVisitor.visitCode() - called when method body starts
override fun visitCode() {
    delegate.visitCode()
    
    if (trackAnnotationInfo != null && !hasInjectedTracking) {
        injectTrackingCall(trackAnnotationInfo!!)  // Inject at method start
    }
}
```

## **Generated Bytecode Example**

**Original method:**
```kotlin
@Track(eventName = "user_login")
fun loginUser(@Param(name = "userId") userId: String, age: Int) {
    // original method body
}
```

**ASM-generated bytecode equivalent:**
```kotlin
fun loginUser(userId: String, age: Int) {
    // 🔥 INJECTED CODE STARTS HERE 🔥
    MethodTrackingManager.track(
        "user_login",                                    // eventName
        mapOf("userId" to userId),                      // parameters (only @Param ones)
        true                                           // includeGlobalParams
    )
    // 🔥 INJECTED CODE ENDS HERE 🔥
    
    // original method body continues unchanged
}
```

## **Detailed Code Injection Process**

### **1. Parameter Map Generation**
```kotlin
private fun generateParametersMap(trackInfo: MethodTrackInfo) {
    val trackedParams = trackInfo.parameters // Only @Param annotated ones
    
    if (trackedParams.isEmpty()) {
        // Generate: Collections.emptyMap()
        delegate.visitMethodInsn(INVOKESTATIC, "java/util/Collections", "emptyMap", "()Ljava/util/Map;", false)
    } else {
        // Generate: new HashMap<String, Object>()
        delegate.visitTypeInsn(NEW, "java/util/HashMap")
        delegate.visitInsn(DUP)
        delegate.visitLdcInsn(trackedParams.size)
        delegate.visitMethodInsn(INVOKESPECIAL, "java/util/HashMap", "<init>", "(I)V", false)
        
        // For each @Param parameter:
        trackedParams.forEach { param ->
            delegate.visitInsn(DUP)                        // Duplicate map reference
            delegate.visitLdcInsn(param.name)              // Push "userId" 
            loadParameterValue(param.index)                // Load actual parameter value
            // Call: map.put("userId", userIdValue)
            delegate.visitMethodInsn(INVOKEINTERFACE, "java/util/Map", "put", "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", true)
            delegate.visitInsn(POP)                        // Remove put() return value
        }
    }
}
```

### **2. Full Method Call Generation**
```kotlin
private fun injectTrackingCall(trackInfo: MethodTrackInfo) {
    // Push eventName: "user_login"
    delegate.visitLdcInsn(trackInfo.eventName)
    
    // Generate parameters map: {"userId": actualUserIdValue}
    generateParametersMap(trackInfo)
    
    // Push includeGlobalParams: true/false 
    delegate.visitInsn(if (trackInfo.includeGlobalParams) ICONST_1 else ICONST_0)
    
    // Call: MethodTrackingManager.track(String, Map, boolean)
    delegate.visitMethodInsn(
        INVOKESTATIC,
        "com/shalan/analytics/core/MethodTrackingManager", 
        "track",
        "(Ljava/lang/String;Ljava/util/Map;Z)V",
        false
    )
}
```

## **Runtime Flow**

### **1. MethodTrackingManager.track() Called**
```kotlin
// Called by ASM-generated code
fun track(eventName: String, parameters: Map<String, Any?>, includeGlobalParams: Boolean) {
    val manager = analyticsManager ?: return
    
    // Serialize parameters using ParameterSerializer chain
    val serializedParams = mutableMapOf<String, Any>()
    parameters.forEach { (key, value) ->
        val serializedValue = serializeParameter(value)  // PrimitiveParameterSerializer, etc.
        if (serializedValue != null) {
            serializedParams[key] = serializedValue
        }
    }
    
    // Forward to analytics providers
    manager.logEvent(eventName, serializedParams)
}
```

### **2. Parameter Serialization Chain**
```kotlin
private fun serializeParameter(value: Any?): Any? {
    synchronized(serializers) {
        for (serializer in serializers) {  // PrimitiveParameterSerializer, SerializableParameterSerializer
            if (serializer.canSerialize(valueType)) {
                return serializer.serialize(value, valueType)
            }
        }
    }
    return value.toString()  // Fallback
}
```

## **Key Design Decisions Explained**

### **1. Why Wrap ALL Methods?**
```kotlin
// We wrap ALL methods, not just @Track ones, because:
return TrackMethodVisitor(api, methodVisitor, name, descriptor, className)
```
- **Reason**: We don't know which methods have `@Track` until we process their annotations
- **Alternative**: Two-pass approach (scan first, instrument second) - more complex
- **Trade-off**: Slight overhead for non-tracked methods vs. simpler code

### **2. Why Inject at `visitCode()`?**
```kotlin
override fun visitCode() {
    delegate.visitCode()
    if (trackAnnotationInfo != null) {
        injectTrackingCall(trackAnnotationInfo!!)  // Inject at method START
    }
}
```
- **`visitCode()`** = called when method body starts (after signature, before first instruction)
- **Perfect timing**: We have annotation data and can inject before any user code
- **Alternative**: Inject at first instruction - more complex, may interfere with user code

### **3. Why Use HashMap Generation vs. Reflection?**
- **Build-time generation**: Creates optimal bytecode, zero runtime reflection
- **Reflection alternative**: Slower, requires method metadata caching
- **Trade-off**: Larger bytecode vs. runtime performance

## **Potential Issues & Limitations**

### **1. Parameter Loading Simplification**
```kotlin
private fun loadParameterValue(parameterIndex: Int) {
    // SIMPLIFIED: Always use ALOAD (assumes objects)
    delegate.visitVarInsn(ALOAD, localVarIndex)
}
```
**Issue**: Doesn't handle primitive types correctly
**Fix needed**: Parse method descriptor to get exact parameter types

### **2. Primitive Boxing Missing**
```kotlin
private fun boxPrimitiveIfNeeded(parameterIndex: Int) {
    // TODO: Box int -> Integer, boolean -> Boolean, etc.
}
```
**Issue**: Primitive parameters won't serialize correctly  
**Fix needed**: Generate boxing code for primitives

### **3. Parameter Count Calculation**
```kotlin
private fun getParameterCount(): Int {
    // SIMPLIFIED: Count characters in descriptor
    return methodDescriptor.count { ... }
}
```
**Issue**: Incorrect for complex types
**Fix needed**: Proper ASM descriptor parsing

## **Data Flow Summary**

```
1. Build Time:
   @Track method → ASM TrackMethodVisitor → Code generation → Modified bytecode

2. Runtime:
   Method called → Injected code executes → MethodTrackingManager.track() 
   → Parameter serialization → AnalyticsManager.logEvent() → Analytics providers

3. Key Components:
   - TrackMethodVisitor: Detects annotations, injects code
   - MethodTrackingManager: Runtime entry point, handles serialization
   - ParameterSerializer: Converts parameters to analytics-safe format
   - AnalyticsManager: Dispatches to actual analytics providers
```

## **Questions for Further Development**

1. **Does this flow make sense?** Any part unclear?
2. **Primitive handling**: Should we implement proper primitive boxing?
3. **Error handling**: Should we add more validation/error cases?
4. **Performance**: Any concerns about the injection overhead?

The core concept is solid - we're essentially doing **compile-time aspect-oriented programming** to inject analytics calls automatically into annotated methods.