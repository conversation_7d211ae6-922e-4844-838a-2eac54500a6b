# Method Tracking Examples

This document describes the method tracking examples in the sample application demonstrating the new `@Track` and `@Param` annotations for method-level analytics.

## Overview

The analytics library now supports method-level tracking using:
- `@Track` annotation to mark methods for tracking
- `@Param` annotation to mark parameters for analytics

## Sample Implementation

### 1. MainActivity Examples

The `MainActivity` contains simple examples of method tracking:

```kotlin
@Track(eventName = "navigation_event", includeGlobalParams = true)
private fun navigateToScreen(
    @Param("destination") destination: String,
    @Param("trigger") trigger: String
) {
    // Navigation logic with automatic tracking
}

@Track(eventName = "user_action", includeGlobalParams = false)
private fun performUserAction(
    @Param("action_type") actionType: String,
    @Param("screen_context") screenContext: String,
    @Param("is_new_user") isNewUser: Boolean
) {
    // User action processing with parameter tracking
}
```

### 2. Comprehensive Examples

The `MethodTrackingExampleActivity` provides extensive examples including:

#### Authentication Tracking
```kotlin
@Track(eventName = "user_login_attempt", includeGlobalParams = true)
private fun performLogin(
    @Param("user_name") userName: String,
    @Param("auth_method") authMethod: String,
    @Param("is_first_login") isFirstLogin: Boolean
) {
    // Login logic with detailed parameter tracking
}
```

#### E-commerce Tracking
```kotlin
@Track(eventName = "purchase_initiated", includeGlobalParams = true)
private fun processPurchase(
    @Param("item_name") itemName: String,
    @Param("price") price: Double,
    @Param("currency") currency: String,
    @Param("quantity") itemCount: Int
) {
    // Purchase processing with financial parameter tracking
}
```

#### Error Tracking
```kotlin
@Track(eventName = "operation_error")
private fun handleOperationError(
    @Param("exception_type") exceptionType: String,
    @Param("error_message") errorMessage: String
) {
    // Error handling with detailed error context
}
```

#### Complex Operations
```kotlin
@Track(eventName = "complex_operation_performed")
private fun performComplexOperation(
    @Param("operation_type") operationType: String,
    @Param("priority") priority: String,
    @Param("retry_count") retryCount: Int,
    @Param("enable_caching") enableCaching: Boolean,
    @Param("metadata") metadata: Map<String, String>
) {
    // Complex business logic with comprehensive parameter tracking
}
```

## Configuration

### Build-Time Configuration
Currently configured via Gradle plugin (will be enabled in future versions):

```kotlin
analytics {
    // Method tracking configuration (coming soon)
    // methodTracking {
    //     enabled = true
    //     maxParametersPerMethod = 10
    //     enableExecutionTiming = true
    //     validateAnnotations = true
    //     excludeMethods = setOf("toString", "hashCode", "equals")
    // }
}
```

### Runtime Configuration
Method tracking is configured at application startup:

```kotlin
ScreenTracking.initialize(
    config = analyticsConfig {
        debugMode = true
        providers.add(InMemoryDebugAnalyticsProvider())
        
        // Method tracking configuration (coming soon)
        // methodTracking {
        //     enabled = true
        //     includeGlobalParamsByDefault = true
        //     maxParametersPerMethod = 10
        //     trackExecutionTime = true
        // }
    }
)
```

## Features Demonstrated

### Parameter Types
The examples show tracking of various parameter types:
- `String` - text values
- `Boolean` - true/false flags  
- `Int` - numeric values
- `Long` - timestamps and large numbers
- `Double` - decimal/price values
- `Map<String, String>` - complex object data

### Tracking Scenarios
- **User Authentication**: Login attempts, success/failure tracking
- **Navigation**: Screen transitions with context
- **Search Operations**: Query tracking with result metrics
- **E-commerce**: Purchase flows with financial data
- **Error Handling**: Exception tracking with detailed context
- **Performance**: Execution timing for complex operations

### Advanced Features
- **Global Parameters**: Automatic inclusion of app-wide context
- **Execution Timing**: Automatic measurement of method execution time
- **Object Serialization**: Support for complex parameter types
- **Error Resilience**: Analytics failures don't crash the app

## Testing the Examples

1. Run the sample app: `./gradlew :app:installDebug`
2. Tap "Method Tracking Examples" button on the main screen
3. Try different actions in the demo activity
4. Check logcat for analytics events (filtered by your analytics provider)

The examples provide comprehensive coverage of method-level analytics tracking patterns suitable for production Android applications.