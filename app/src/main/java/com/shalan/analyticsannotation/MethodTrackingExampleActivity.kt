package com.shalan.analyticsannotation

import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.shalan.analytics.annotation.Param
import com.shalan.analytics.annotation.Track
import com.shalan.analytics.annotation.TrackScreen

/**
 * Activity demonstrating comprehensive method tracking with @Track and @Param annotations.
 * This shows various use cases for the method-level analytics tracking feature.
 */
@TrackScreen(screenName = "Method Tracking Demo")
class MethodTrackingExampleActivity : AppCompatActivity() {
    private lateinit var userNameInput: EditText
    private lateinit var searchInput: EditText

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_method_tracking_example)

        initializeViews()
        setupButtonListeners()

        // Track the screen initialization
        onScreenInitialized(System.currentTimeMillis())
    }

    private fun initializeViews() {
        userNameInput = findViewById(R.id.user_name_input)
        searchInput = findViewById(R.id.search_input)
    }

    private fun setupButtonListeners() {
        findViewById<Button>(R.id.login_button).setOnClickListener {
            val userName = userNameInput.text.toString()
            if (userName.isNotEmpty()) {
                performLogin(userName, "password_auth", isFirstLogin = true)
            }
        }

        findViewById<Button>(R.id.search_button).setOnClickListener {
            val query = searchInput.text.toString()
            if (query.isNotEmpty()) {
                performSearch(query, "user_initiated", maxResults = 20)
            }
        }

        findViewById<Button>(R.id.purchase_button).setOnClickListener {
            processPurchase("premium_subscription", 9.99, "USD", itemCount = 1)
        }

        findViewById<Button>(R.id.error_button).setOnClickListener {
            simulateErrorScenario()
        }

        findViewById<Button>(R.id.complex_operation_button).setOnClickListener {
            performComplexOperation(
                operationType = "data_processing",
                priority = "high",
                retryCount = 3,
                enableCaching = true,
                metadata = mapOf("source" to "user_action", "feature" to "demo"),
            )
        }
    }

    /**
     * Example: Basic method tracking without parameters
     */
    @Track(eventName = "screen_initialized")
    private fun onScreenInitialized(timestamp: Long) {
        println("Screen initialized at: $timestamp")
    }

    /**
     * Example: Method tracking with multiple parameter types
     */
    @Track(eventName = "user_login_attempt", includeGlobalParams = true)
    private fun performLogin(
        @Param("user_name") userName: String,
        @Param("auth_method") authMethod: String,
        @Param("is_first_login") isFirstLogin: Boolean,
    ) {
        // Simulate login processing
        Thread.sleep(150) // Show execution timing

        val success = userName.length > 3
        if (success) {
            onLoginSuccess(userName)
            Toast.makeText(this, "Login successful for $userName", Toast.LENGTH_SHORT).show()
        } else {
            onLoginFailure("invalid_username", userName.length)
            Toast.makeText(this, "Login failed - username too short", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Example: Tracking success scenarios
     */
    @Track(eventName = "login_success")
    private fun onLoginSuccess(
        @Param("user_name") userName: String,
    ) {
        // Track successful login
        updateUserSession(userName)
    }

    /**
     * Example: Tracking failure scenarios with error details
     */
    @Track(eventName = "login_failure", includeGlobalParams = false)
    private fun onLoginFailure(
        @Param("error_reason") errorReason: String,
        @Param("username_length") usernameLength: Int,
    ) {
        // Track login failure with context
        println("Login failed: $errorReason (username length: $usernameLength)")
    }

    /**
     * Example: Method with various primitive parameter types
     */
    @Track(eventName = "search_performed")
    private fun performSearch(
        @Param("query") searchQuery: String,
        @Param("search_type") searchType: String,
        @Param("max_results") maxResults: Int,
    ) {
        // Simulate search processing
        Thread.sleep(200)

        val resultCount = searchQuery.length * 3 // Mock result calculation
        displaySearchResults(searchQuery, resultCount, System.currentTimeMillis())

        Toast.makeText(this, "Found $resultCount results for '$searchQuery'", Toast.LENGTH_SHORT).show()
    }

    /**
     * Example: Method with numeric parameters and timestamp
     */
    @Track(eventName = "search_results_displayed")
    private fun displaySearchResults(
        @Param("original_query") query: String,
        @Param("result_count") count: Int,
        @Param("completion_time") completionTime: Long,
    ) {
        // Display results logic
        println("Search completed: query='$query', results=$count, time=$completionTime")
    }

    /**
     * Example: E-commerce tracking with price and currency
     */
    @Track(eventName = "purchase_initiated", includeGlobalParams = true)
    private fun processPurchase(
        @Param("item_name") itemName: String,
        @Param("price") price: Double,
        @Param("currency") currency: String,
        @Param("quantity") itemCount: Int,
    ) {
        // Simulate purchase processing
        Thread.sleep(300)

        val success = price > 0
        if (success) {
            onPurchaseComplete(itemName, price, currency)
            Toast.makeText(this, "Purchase successful: $itemName", Toast.LENGTH_SHORT).show()
        } else {
            onPurchaseError("invalid_price", itemName)
        }
    }

    @Track(eventName = "purchase_completed")
    private fun onPurchaseComplete(
        @Param("item") item: String,
        @Param("amount") amount: Double,
        @Param("currency") currency: String,
    ) {
        println("Purchase completed: $item for $amount $currency")
    }

    @Track(eventName = "purchase_error")
    private fun onPurchaseError(
        @Param("error_type") errorType: String,
        @Param("item_name") itemName: String,
    ) {
        println("Purchase error: $errorType for $itemName")
    }

    /**
     * Example: Method that demonstrates error handling in tracking
     */
    @Track(eventName = "error_scenario_triggered")
    private fun simulateErrorScenario() {
        try {
            // Simulate some operation that might fail
            val result = 10 / 0 // This will throw ArithmeticException
            handleSuccessfulOperation(result)
        } catch (e: Exception) {
            handleOperationError(e.javaClass.simpleName, e.message ?: "Unknown error")
        }
    }

    @Track(eventName = "operation_success")
    private fun handleSuccessfulOperation(
        @Param("result") result: Int,
    ) {
        Toast.makeText(this, "Operation successful: $result", Toast.LENGTH_SHORT).show()
    }

    @Track(eventName = "operation_error")
    private fun handleOperationError(
        @Param("exception_type") exceptionType: String,
        @Param("error_message") errorMessage: String,
    ) {
        Toast.makeText(this, "Error: $exceptionType - $errorMessage", Toast.LENGTH_LONG).show()
    }

    /**
     * Example: Complex method with many parameters to test limits
     */
    @Track(eventName = "complex_operation_performed")
    private fun performComplexOperation(
        @Param("operation_type") operationType: String,
        @Param("priority") priority: String,
        @Param("retry_count") retryCount: Int,
        @Param("enable_caching") enableCaching: Boolean,
        @Param("metadata") metadata: Map<String, String>,
    ) {
        // Simulate complex processing
        Thread.sleep(500) // Longer operation to show execution timing

        val result =
            ComplexOperationResult(
                operationType = operationType,
                success = true,
                processingTime = 500L,
                cacheUsed = enableCaching,
            )

        onComplexOperationComplete(result)
        Toast.makeText(this, "Complex operation completed: $operationType", Toast.LENGTH_SHORT).show()
    }

    @Track(eventName = "complex_operation_completed")
    private fun onComplexOperationComplete(
        @Param("result") result: ComplexOperationResult,
    ) {
        println("Complex operation result: $result")
    }

    /**
     * Example: Private method tracking (to test access modifier handling)
     */
    @Track(eventName = "session_updated")
    private fun updateUserSession(
        @Param("user_id") userId: String,
    ) {
        // Update user session logic
        println("User session updated for: $userId")
    }

    /**
     * Data class for demonstrating object parameter serialization
     */
    data class ComplexOperationResult(
        val operationType: String,
        val success: Boolean,
        val processingTime: Long,
        val cacheUsed: Boolean,
    )
}
