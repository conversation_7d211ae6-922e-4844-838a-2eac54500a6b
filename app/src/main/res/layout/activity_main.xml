<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <Button
        android:id="@+id/example_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Open Example"
        app:layout_constraintBottom_toTopOf="@id/composable_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />

    <Button
        android:id="@+id/composable_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Open Composable"
        app:layout_constraintBottom_toTopOf="@id/exclude_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/example_button"
        tools:ignore="HardcodedText" />

    <Button
        android:id="@+id/exclude_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Open Excluded Activity"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/composable_button"
        tools:ignore="HardcodedText" />

</androidx.constraintlayout.widget.ConstraintLayout>
