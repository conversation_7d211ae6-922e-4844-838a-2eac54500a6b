<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Annotation - Architecture Graph</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
        }

        .description {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-group label {
            font-size: 12px;
            color: #555;
            font-weight: 500;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        button:hover {
            background: #0056b3;
        }

        button.secondary {
            background: #6c757d;
        }

        button.secondary:hover {
            background: #545b62;
        }

        input[type="range"] {
            width: 100px;
        }

        select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
        }

        #graph-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node:hover {
            stroke-width: 3px;
        }

        .node-module {
            fill: #e3f2fd;
            stroke: #2196f3;
            stroke-width: 2px;
        }

        .node-class {
            stroke-width: 1.5px;
        }

        .node-interface {
            fill: #fff3e0;
            stroke: #ff9800;
        }

        .node-abstract {
            fill: #fce4ec;
            stroke: #e91e63;
        }

        .node-annotation {
            fill: #f3e5f5;
            stroke: #9c27b0;
        }

        .node-object {
            fill: #e8f5e8;
            stroke: #4caf50;
        }

        .node-data {
            fill: #fff8e1;
            stroke: #ffc107;
        }

        .node-test {
            fill: #f5f5f5;
            stroke: #757575;
        }

        .node-regular {
            fill: #ffffff;
            stroke: #333333;
        }

        .link {
            fill: none;
            stroke-width: 1.5px;
            marker-end: url(#arrowhead);
            transition: all 0.3s ease;
        }

        .link-inheritance {
            stroke: #2196f3;
            stroke-dasharray: none;
        }

        .link-implements {
            stroke: #4caf50;
            stroke-dasharray: 5,5;
        }

        .link-composition {
            stroke: #ff9800;
            stroke-dasharray: none;
        }

        .link-dependency {
            stroke: #9c27b0;
            stroke-dasharray: 2,3;
        }

        .link-module {
            stroke: #757575;
            stroke-width: 3px;
        }

        .node-text {
            fill: #333;
            font-size: 12px;
            font-weight: 500;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
        }

        .module-text {
            fill: #2196f3;
            font-size: 14px;
            font-weight: bold;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .stats-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .collapsible {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin: 5px 0;
        }

        .collapsible-header {
            background: #e9ecef;
            padding: 8px 12px;
            cursor: pointer;
            font-weight: 500;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .collapsible-content {
            padding: 8px 12px;
            display: none;
            font-size: 11px;
            color: #666;
        }

        .collapsible.expanded .collapsible-content {
            display: block;
        }

        .arrow {
            transition: transform 0.3s ease;
        }

        .collapsible.expanded .arrow {
            transform: rotate(90deg);
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .legend, .stats {
                position: relative;
                margin: 10px 0;
                top: auto;
                right: auto;
                bottom: auto;
                left: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Analytics Annotation Library - Architecture Graph</h1>
        <div class="description">
            Interactive visualization of class relationships and module dependencies in the Android Analytics Library
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Layout:</label>
                <select id="layoutSelect">
                    <option value="force">Force Layout</option>
                    <option value="hierarchical">Hierarchical</option>
                    <option value="circular">Circular</option>
                </select>
            </div>
            <div class="control-group">
                <label>Strength:</label>
                <input type="range" id="strengthSlider" min="0.1" max="2" step="0.1" value="1">
                <span id="strengthValue">1.0</span>
            </div>
            <div class="control-group">
                <label>Distance:</label>
                <input type="range" id="distanceSlider" min="50" max="300" step="10" value="150">
                <span id="distanceValue">150</span>
            </div>
            <div class="control-group">
                <button id="resetButton">Reset View</button>
                <button id="toggleModules" class="secondary">Toggle Modules</button>
                <button id="collapseAll" class="secondary">Collapse All</button>
            </div>
        </div>

        <div id="graph-container">
            <div class="legend">
                <div class="legend-title">Node Types</div>
                <div class="legend-item">
                    <div class="legend-color node-module" style="border: 2px solid #2196f3;"></div>
                    <span>Module</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-interface" style="border: 1px solid #ff9800;"></div>
                    <span>Interface</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-abstract" style="border: 1px solid #e91e63;"></div>
                    <span>Abstract Class</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-annotation" style="border: 1px solid #9c27b0;"></div>
                    <span>Annotation</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-object" style="border: 1px solid #4caf50;"></div>
                    <span>Object/Singleton</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-data" style="border: 1px solid #ffc107;"></div>
                    <span>Data Class</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-test" style="border: 1px solid #757575;"></div>
                    <span>Test Class</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color node-regular" style="border: 1px solid #333;"></div>
                    <span>Regular Class</span>
                </div>
                
                <div class="legend-title" style="margin-top: 15px;">Edge Types</div>
                <div class="legend-item">
                    <div style="width: 20px; height: 2px; background: #2196f3; margin-right: 6px;"></div>
                    <span>Inheritance</span>
                </div>
                <div class="legend-item">
                    <div style="width: 20px; height: 2px; background: #4caf50; margin-right: 6px; background-image: repeating-linear-gradient(90deg, transparent, transparent 2px, white 2px, white 4px);"></div>
                    <span>Implements</span>
                </div>
                <div class="legend-item">
                    <div style="width: 20px; height: 2px; background: #ff9800; margin-right: 6px;"></div>
                    <span>Composition</span>
                </div>
                <div class="legend-item">
                    <div style="width: 20px; height: 2px; background: #9c27b0; margin-right: 6px; background-image: repeating-linear-gradient(90deg, transparent, transparent 1px, white 1px, white 2px);"></div>
                    <span>Dependency</span>
                </div>
            </div>

            <div class="stats">
                <div class="stats-title">Statistics</div>
                <div id="statsContent"></div>
            </div>
        </div>
    </div>

    <script>
        // Data structure containing all the class and module information
        const data = {
            modules: {
                annotation: { name: "annotation", color: "#e3f2fd", description: "Contains annotation definitions (@TrackScreen, @Track, @Param)" },
                app: { name: "app", color: "#fff3e0", description: "Sample application demonstrating library usage" },
                compose: { name: "compose", color: "#f3e5f5", description: "Jetpack Compose integration" },
                core: { name: "core", color: "#e8f5e8", description: "Core analytics functionality and lifecycle callbacks" },
                plugin: { name: "plugin", color: "#fff8e1", description: "Gradle plugin for bytecode instrumentation" }
            },
            nodes: [
                // Module nodes
                { id: "module:annotation", type: "module", module: "annotation", name: "annotation", size: 40 },
                { id: "module:app", type: "module", module: "app", name: "app", size: 40 },
                { id: "module:compose", type: "module", module: "compose", name: "compose", size: 40 },
                { id: "module:core", type: "module", module: "core", name: "core", size: 40 },
                { id: "module:plugin", type: "module", module: "plugin", name: "plugin", size: 40 },

                // Annotation module classes
                { id: "Param", type: "annotation", module: "annotation", name: "Param", package: "com.shalan.analytics.annotation", size: 20, parent: "module:annotation" },
                { id: "Track", type: "annotation", module: "annotation", name: "Track", package: "com.shalan.analytics.annotation", size: 20, parent: "module:annotation" },
                { id: "TrackScreen", type: "annotation", module: "annotation", name: "TrackScreen", package: "com.shalan.analytics.annotation", size: 25, parent: "module:annotation" },

                // App module classes
                { id: "ComposableActivity", type: "class", module: "app", name: "ComposableActivity", package: "com.shalan.analyticsannotation", size: 20, parent: "module:app" },
                { id: "ExampleActivity", type: "class", module: "app", name: "ExampleActivity", package: "com.shalan.analyticsannotation", size: 20, parent: "module:app" },
                { id: "ExampleFragment", type: "class", module: "app", name: "ExampleFragment", package: "com.shalan.analyticsannotation", size: 20, parent: "module:app" },
                { id: "MainActivity", type: "class", module: "app", name: "MainActivity", package: "com.shalan.analyticsannotation", size: 25, parent: "module:app" },
                { id: "SampleApp", type: "class", module: "app", name: "SampleApp", package: "com.shalan.analyticsannotation", size: 30, parent: "module:app" },

                // Compose module classes
                { id: "TrackScreenComposable", type: "annotation", module: "compose", name: "TrackScreenComposable", package: "com.shalan.analytics.compose", size: 20, parent: "module:compose" },
                { id: "TrackScreenView", type: "function", module: "compose", name: "TrackScreenView", package: "com.shalan.analytics.compose", size: 25, parent: "module:compose" },

                // Core module classes
                { id: "AnalyticsConfig", type: "class", module: "core", name: "AnalyticsConfig", package: "com.shalan.analytics.core", size: 25, parent: "module:core" },
                { id: "AnalyticsManager", type: "interface", module: "core", name: "AnalyticsManager", package: "com.shalan.analytics.core", size: 30, parent: "module:core" },
                { id: "AnalyticsManagerImpl", type: "class", module: "core", name: "AnalyticsManagerImpl", package: "com.shalan.analytics.core", size: 25, parent: "module:core" },
                { id: "AnalyticsProvider", type: "interface", module: "core", name: "AnalyticsProvider", package: "com.shalan.analytics.core", size: 25, parent: "module:core" },
                { id: "DebugAnalyticsProvider", type: "interface", module: "core", name: "DebugAnalyticsProvider", package: "com.shalan.analytics.core", size: 20, parent: "module:core" },
                { id: "InMemoryDebugAnalyticsProvider", type: "class", module: "core", name: "InMemoryDebugAnalyticsProvider", package: "com.shalan.analytics.core", size: 25, parent: "module:core" },
                { id: "MethodTrackingManager", type: "object", module: "core", name: "MethodTrackingManager", package: "com.shalan.analytics.core", size: 30, parent: "module:core" },
                { id: "ParameterSerializer", type: "interface", module: "core", name: "ParameterSerializer", package: "com.shalan.analytics.core", size: 25, parent: "module:core" },
                { id: "PrimitiveParameterSerializer", type: "class", module: "core", name: "PrimitiveParameterSerializer", package: "com.shalan.analytics.core", size: 20, parent: "module:core" },
                { id: "ScreenTracking", type: "object", module: "core", name: "ScreenTracking", package: "com.shalan.analytics.core", size: 35, parent: "module:core" },
                { id: "SerializableParameterSerializer", type: "class", module: "core", name: "SerializableParameterSerializer", package: "com.shalan.analytics.core", size: 20, parent: "module:core" },
                { id: "TrackedScreenParamsProvider", type: "interface", module: "core", name: "TrackedScreenParamsProvider", package: "com.shalan.analytics.core", size: 20, parent: "module:core" },

                // Plugin module classes
                { id: "AnalyticsPlugin", type: "class", module: "plugin", name: "AnalyticsPlugin", package: "com.shalan.analytics.plugin", size: 30, parent: "module:plugin" },
                { id: "AnalyticsPluginExtension", type: "class", module: "plugin", name: "AnalyticsPluginExtension", package: "com.shalan.analytics.plugin", size: 25, parent: "module:plugin" },
                { id: "AnalyticsClassVisitorFactory", type: "abstract", module: "plugin", name: "AnalyticsClassVisitorFactory", package: "com.shalan.analytics.plugin.instrumentation", size: 25, parent: "module:plugin" },
                { id: "TrackMethodVisitor", type: "class", module: "plugin", name: "TrackMethodVisitor", package: "com.shalan.analytics.plugin.instrumentation", size: 25, parent: "module:plugin" },
                { id: "MethodTrackInfo", type: "data", module: "plugin", name: "MethodTrackInfo", package: "com.shalan.analytics.plugin.instrumentation", size: 20, parent: "module:plugin" }
            ],
            links: [
                // Module dependencies
                { source: "module:app", target: "module:annotation", type: "module_dependency", strength: 0.5 },
                { source: "module:app", target: "module:core", type: "module_dependency", strength: 0.8 },
                { source: "module:app", target: "module:compose", type: "module_dependency", strength: 0.3 },
                { source: "module:compose", target: "module:core", type: "module_dependency", strength: 0.6 },
                { source: "module:plugin", target: "module:core", type: "module_dependency", strength: 0.4 },

                // Inheritance relationships
                { source: "AnalyticsManagerImpl", target: "AnalyticsManager", type: "implements" },
                { source: "DebugAnalyticsProvider", target: "AnalyticsProvider", type: "extends" },
                { source: "InMemoryDebugAnalyticsProvider", target: "DebugAnalyticsProvider", type: "implements" },
                { source: "PrimitiveParameterSerializer", target: "ParameterSerializer", type: "implements" },
                { source: "SerializableParameterSerializer", target: "ParameterSerializer", type: "implements" },
                { source: "MainActivity", target: "TrackedScreenParamsProvider", type: "implements" },

                // Composition relationships
                { source: "AnalyticsConfig", target: "AnalyticsProvider", type: "composition" },
                { source: "AnalyticsManagerImpl", target: "AnalyticsProvider", type: "composition" },
                { source: "MethodTrackingManager", target: "ParameterSerializer", type: "composition" },
                { source: "MethodTrackingManager", target: "AnalyticsManager", type: "composition" },
                { source: "ScreenTracking", target: "AnalyticsManager", type: "composition" },
                { source: "ScreenTracking", target: "AnalyticsConfig", type: "composition" },

                // Key dependencies
                { source: "SampleApp", target: "ScreenTracking", type: "dependency" },
                { source: "SampleApp", target: "InMemoryDebugAnalyticsProvider", type: "dependency" },
                { source: "TrackScreenView", target: "ScreenTracking", type: "dependency" },
                { source: "ScreenTracking", target: "MethodTrackingManager", type: "dependency" },
                { source: "AnalyticsPlugin", target: "AnalyticsClassVisitorFactory", type: "dependency" },
                { source: "AnalyticsClassVisitorFactory", target: "TrackMethodVisitor", type: "dependency" },

                // Cross-module annotation usage
                { source: "ComposableActivity", target: "TrackScreen", type: "uses_annotation" },
                { source: "ExampleActivity", target: "TrackScreen", type: "uses_annotation" },
                { source: "ExampleFragment", target: "TrackScreen", type: "uses_annotation" },
                { source: "MainActivity", target: "TrackScreen", type: "uses_annotation" }
            ]
        };

        // Set up the SVG and simulation
        const container = d3.select('#graph-container');
        const containerRect = container.node().getBoundingClientRect();
        const width = containerRect.width;
        const height = 800;

        const svg = container.append('svg')
            .attr('width', width)
            .attr('height', height);

        // Define arrowhead marker
        svg.append('defs').append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 15)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('class', 'arrowhead')
            .style('fill', '#666')
            .style('stroke', 'none');

        const g = svg.append('g');

        // Zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                g.attr('transform', event.transform);
            });

        svg.call(zoom);

        // Tooltip
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);

        let simulation;
        let showModules = true;
        let currentLayout = 'force';

        // Create simulation
        function createSimulation() {
            simulation = d3.forceSimulation()
                .force('link', d3.forceLink().id(d => d.id).distance(150))
                .force('charge', d3.forceManyBody().strength(-500))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => d.size + 5));
        }

        // Filter data based on current view settings
        function getFilteredData() {
            let nodes = [...data.nodes];
            let links = [...data.links];

            if (!showModules) {
                nodes = nodes.filter(d => d.type !== 'module');
                links = links.filter(d => d.type !== 'module_dependency');
            }

            return { nodes, links };
        }

        // Get node class based on type
        function getNodeClass(d) {
            const baseClass = 'node';
            if (d.type === 'module') return `${baseClass} node-module`;
            if (d.type === 'interface') return `${baseClass} node-interface`;
            if (d.type === 'abstract') return `${baseClass} node-abstract`;
            if (d.type === 'annotation') return `${baseClass} node-annotation`;
            if (d.type === 'object') return `${baseClass} node-object`;
            if (d.type === 'data') return `${baseClass} node-data`;
            if (d.name && d.name.toLowerCase().includes('test')) return `${baseClass} node-test`;
            return `${baseClass} node-regular`;
        }

        // Get link class based on type
        function getLinkClass(d) {
            const baseClass = 'link';
            if (d.type === 'implements' || d.type === 'extends') return `${baseClass} link-inheritance`;
            if (d.type === 'composition') return `${baseClass} link-composition`;
            if (d.type === 'dependency' || d.type === 'uses_annotation') return `${baseClass} link-dependency`;
            if (d.type === 'module_dependency') return `${baseClass} link-module`;
            return baseClass;
        }

        // Generate tooltip content
        function getTooltipContent(d) {
            if (d.type === 'module') {
                const moduleClasses = data.nodes.filter(n => n.module === d.name && n.type !== 'module');
                return `
                    <div class="collapsible">
                        <div class="collapsible-header">
                            <strong>Module: ${d.name}</strong>
                            <span class="arrow">▶</span>
                        </div>
                        <div class="collapsible-content">
                            <div><strong>Description:</strong> ${data.modules[d.name].description}</div>
                            <div><strong>Classes:</strong> ${moduleClasses.length}</div>
                            <div><strong>Types:</strong></div>
                            <ul>
                                ${[...new Set(moduleClasses.map(n => n.type))].map(type => 
                                    `<li>${type}: ${moduleClasses.filter(n => n.type === type).length}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    </div>
                `;
            } else {
                const relationships = data.links.filter(l => l.source.id === d.id || l.target.id === d.id);
                const incoming = relationships.filter(l => l.target.id === d.id);
                const outgoing = relationships.filter(l => l.source.id === d.id);
                
                return `
                    <div class="collapsible">
                        <div class="collapsible-header">
                            <strong>${d.name}</strong>
                            <span class="arrow">▶</span>
                        </div>
                        <div class="collapsible-content">
                            <div><strong>Type:</strong> ${d.type}</div>
                            <div><strong>Module:</strong> ${d.module}</div>
                            <div><strong>Package:</strong> ${d.package}</div>
                            <div><strong>Relationships:</strong> ${relationships.length}</div>
                            ${incoming.length > 0 ? `
                                <div><strong>Used by:</strong></div>
                                <ul>
                                    ${incoming.map(l => `<li>${l.source.name} (${l.type})</li>`).join('')}
                                </ul>
                            ` : ''}
                            ${outgoing.length > 0 ? `
                                <div><strong>Uses:</strong></div>
                                <ul>
                                    ${outgoing.map(l => `<li>${l.target.name} (${l.type})</li>`).join('')}
                                </ul>
                            ` : ''}
                        </div>
                    </div>
                `;
            }
        }

        // Render the graph
        function render() {
            const { nodes, links } = getFilteredData();

            // Clear previous elements
            g.selectAll('.link').remove();
            g.selectAll('.node').remove();
            g.selectAll('.node-text').remove();

            // Create links
            const link = g.selectAll('.link')
                .data(links)
                .enter().append('line')
                .attr('class', getLinkClass);

            // Create nodes
            const node = g.selectAll('.node')
                .data(nodes)
                .enter().append('circle')
                .attr('class', getNodeClass)
                .attr('r', d => d.size)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // Create node labels
            const text = g.selectAll('.node-text')
                .data(nodes)
                .enter().append('text')
                .attr('class', d => d.type === 'module' ? 'node-text module-text' : 'node-text')
                .text(d => d.name)
                .style('font-size', d => d.type === 'module' ? '14px' : '12px');

            // Add interactions
            node.on('mouseover', function(event, d) {
                tooltip.transition().duration(200).style('opacity', .9);
                tooltip.html(getTooltipContent(d))
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 28) + 'px');
                
                // Highlight connected nodes and links
                const connectedLinks = links.filter(l => l.source.id === d.id || l.target.id === d.id);
                const connectedNodes = new Set();
                connectedLinks.forEach(l => {
                    connectedNodes.add(l.source.id);
                    connectedNodes.add(l.target.id);
                });
                
                node.style('opacity', n => connectedNodes.has(n.id) ? 1 : 0.3);
                link.style('opacity', l => l.source.id === d.id || l.target.id === d.id ? 1 : 0.1);
                text.style('opacity', n => connectedNodes.has(n.id) ? 1 : 0.3);
            })
            .on('mouseout', function(event, d) {
                tooltip.transition().duration(500).style('opacity', 0);
                node.style('opacity', 1);
                link.style('opacity', 1);
                text.style('opacity', 1);
            })
            .on('click', function(event, d) {
                // Zoom to node
                const transform = d3.zoomIdentity
                    .translate(width / 2 - d.x, height / 2 - d.y)
                    .scale(1.5);
                svg.transition().duration(750).call(zoom.transform, transform);
            });

            // Handle tooltip collapsible sections
            tooltip.on('click', function() {
                const collapsibles = tooltip.selectAll('.collapsible');
                collapsibles.classed('expanded', function() {
                    return !d3.select(this).classed('expanded');
                });
            });

            // Apply layout
            if (currentLayout === 'hierarchical') {
                applyHierarchicalLayout(nodes, links);
            } else if (currentLayout === 'circular') {
                applyCircularLayout(nodes);
            } else {
                applyForceLayout(nodes, links);
            }

            // Update simulation
            simulation.nodes(nodes);
            simulation.force('link').links(links);

            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);

                text
                    .attr('x', d => d.x)
                    .attr('y', d => d.y);
            });

            updateStats(nodes, links);
        }

        // Apply different layouts
        function applyForceLayout(nodes, links) {
            // Standard force layout - already handled by simulation
        }

        function applyHierarchicalLayout(nodes, links) {
            const levels = {};
            const visited = new Set();
            
            // Find root nodes (modules or nodes with no incoming edges)
            const rootNodes = nodes.filter(n => 
                n.type === 'module' || 
                !links.some(l => l.target.id === n.id)
            );
            
            // BFS to assign levels
            function assignLevel(node, level) {
                if (visited.has(node.id)) return;
                visited.add(node.id);
                
                if (!levels[level]) levels[level] = [];
                levels[level].push(node);
                
                const children = links
                    .filter(l => l.source.id === node.id)
                    .map(l => nodes.find(n => n.id === l.target.id))
                    .filter(n => n && !visited.has(n.id));
                
                children.forEach(child => assignLevel(child, level + 1));
            }
            
            rootNodes.forEach(node => assignLevel(node, 0));
            
            // Position nodes by level
            Object.keys(levels).forEach(level => {
                const levelNodes = levels[level];
                const y = (parseInt(level) + 1) * height / (Object.keys(levels).length + 1);
                
                levelNodes.forEach((node, i) => {
                    node.x = (i + 1) * width / (levelNodes.length + 1);
                    node.y = y;
                    node.fx = node.x;
                    node.fy = node.y;
                });
            });
        }

        function applyCircularLayout(nodes) {
            const moduleNodes = nodes.filter(n => n.type === 'module');
            const classNodes = nodes.filter(n => n.type !== 'module');
            
            // Position modules in outer circle
            moduleNodes.forEach((node, i) => {
                const angle = (i * 2 * Math.PI) / moduleNodes.length;
                node.x = width/2 + Math.cos(angle) * 300;
                node.y = height/2 + Math.sin(angle) * 300;
                node.fx = node.x;
                node.fy = node.y;
            });
            
            // Position class nodes around their modules
            moduleNodes.forEach(module => {
                const moduleClasses = classNodes.filter(n => n.module === module.name);
                moduleClasses.forEach((node, i) => {
                    const angle = (i * 2 * Math.PI) / moduleClasses.length;
                    const radius = 80 + (moduleClasses.length * 2);
                    node.x = module.x + Math.cos(angle) * radius;
                    node.y = module.y + Math.sin(angle) * radius;
                    node.fx = node.x;
                    node.fy = node.y;
                });
            });
        }

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            if (currentLayout === 'force') {
                d.fx = null;
                d.fy = null;
            }
        }

        // Update statistics
        function updateStats(nodes, links) {
            const stats = {
                total_nodes: nodes.length,
                total_links: links.length,
                modules: nodes.filter(n => n.type === 'module').length,
                classes: nodes.filter(n => n.type === 'class').length,
                interfaces: nodes.filter(n => n.type === 'interface').length,
                annotations: nodes.filter(n => n.type === 'annotation').length,
                objects: nodes.filter(n => n.type === 'object').length
            };

            const statsHtml = Object.entries(stats)
                .map(([key, value]) => `<div><strong>${key.replace(/_/g, ' ')}:</strong> ${value}</div>`)
                .join('');

            d3.select('#statsContent').html(statsHtml);
        }

        // Control handlers
        d3.select('#layoutSelect').on('change', function() {
            currentLayout = this.value;
            render();
        });

        d3.select('#strengthSlider').on('input', function() {
            const value = +this.value;
            d3.select('#strengthValue').text(value.toFixed(1));
            simulation.force('charge').strength(-500 * value);
            simulation.alpha(0.3).restart();
        });

        d3.select('#distanceSlider').on('input', function() {
            const value = +this.value;
            d3.select('#distanceValue').text(value);
            simulation.force('link').distance(value);
            simulation.alpha(0.3).restart();
        });

        d3.select('#resetButton').on('click', function() {
            const transform = d3.zoomIdentity;
            svg.transition().duration(750).call(zoom.transform, transform);
            
            // Clear fixed positions
            simulation.nodes().forEach(d => {
                d.fx = null;
                d.fy = null;
            });
            simulation.alpha(0.5).restart();
        });

        d3.select('#toggleModules').on('click', function() {
            showModules = !showModules;
            this.textContent = showModules ? 'Hide Modules' : 'Show Modules';
            render();
        });

        d3.select('#collapseAll').on('click', function() {
            d3.selectAll('.collapsible').classed('expanded', false);
        });

        // Initialize
        createSimulation();
        render();

        // Handle window resize
        window.addEventListener('resize', function() {
            const newRect = container.node().getBoundingClientRect();
            svg.attr('width', newRect.width);
            simulation.force('center', d3.forceCenter(newRect.width / 2, height / 2));
            simulation.alpha(0.3).restart();
        });
    </script>
</body>
</html>